{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE COMBINEE E-SYNDIC + GESTION LOCATIVE\n", "\n", "## Fonctionnalites implementees:\n", "\n", "1. **Fusion des dataframes programme et locataires** - Affiche pour chaque programme le nombre de proprietaires et locataires avec le type de bien\n", "2. **Proprietaires avec nombre de biens et locataires** - Pour chaque proprietaire individuellement : nom, nombre de biens possedes, nombre de locataires\n", "3. **Nombre d'evenements par programme** - Dataframe montrant le nombre d'evenements par programme\n", "4. **Evenements par personne** - Pour chaque evenement, voir qui l'a organise et faire un total par personne\n", "5. **Nombre d'incidents par programme** - Dataframe montrant le nombre d'incidents par programme"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions de chargement"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def load_csv_direct(csv_file):\n", "    try:\n", "        df_raw = pd.read_csv(csv_file)\n", "        print(f\"Fichier charge: {csv_file}\")\n", "        print(f\"Nombre de lignes: {len(df_raw)}\")\n", "        return df_raw\n", "    except Exception as e:\n", "        print(f\"Erreur: {str(e)}\")\n", "        return None\n", "\n", "def parse_json_columns(df, json_columns):\n", "    df_copy = df.copy()\n", "    for col in json_columns:\n", "        if col in df_copy.columns:\n", "            df_copy[col] = df_copy[col].apply(lambda x: ast.literal_eval(x) if pd.notna(x) and x != '[]' and x != '{}' else [])\n", "    return df_copy\n", "\n", "def explode_column(df, col_name, parent_col='Parent'):\n", "    rows = []\n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    return pd.DataFrame(rows)\n", "\n", "def is_valid_value(value):\n", "    \"\"\"Verifie si une valeur est valide pour construire un nom\"\"\"\n", "    try:\n", "        if pd.isna(value):\n", "            return False\n", "        if isinstance(value, (list, np.ndarray)):\n", "            return len(value) > 0\n", "        if isinstance(value, str):\n", "            return value.strip() != ''\n", "        return str(value).strip() != ''\n", "    except:\n", "        return False\n", "\n", "def extract_name_from_row(row, exclude_cols=['Bien', 'Programme']):\n", "    \"\"\"Extrait un nom a partir d'une ligne de dataframe\"\"\"\n", "    nom_parts = []\n", "    for col in row.index:\n", "        if col not in exclude_cols and is_valid_value(row[col]):\n", "            try:\n", "                if isinstance(row[col], (list, np.ndarray)):\n", "                    if len(row[col]) > 0:\n", "                        nom_parts.append(str(row[col][0]).strip())\n", "                else:\n", "                    nom_parts.append(str(row[col]).strip())\n", "            except:\n", "                continue\n", "    return ' '.join(nom_parts[:2]) if nom_parts else 'Personne_Inconnue'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement des donnees"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CHARGEMENT GESTION LOCATIVE ===\n", "Fichier charge: G_locative.csv\n", "Nombre de lignes: 42\n", "Biens charges: 42\n", "Proprietaires: 34\n", "Locataires: 10\n"]}], "source": ["# Chargement Gestion Locative\n", "print(\"=== CHARGEMENT GESTION LOCATIVE ===\")\n", "df_biens_raw = load_csv_direct('G_locative.csv')\n", "\n", "if df_biens_raw is not None:\n", "    json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']\n", "    df_biens = parse_json_columns(df_biens_raw, json_columns)\n", "    \n", "    df_biens.rename(columns={\n", "        'libelle': 'Bien',\n", "        'adresse': '<PERSON>ress<PERSON>',\n", "        'proprietaires': 'Proprietaires',\n", "        'locataires': 'Locataires',\n", "        'charges': 'Charges',\n", "        'totalCharges': 'TotalCharges',\n", "        'totaLoyer': 'TotalLoyer',\n", "        'totalImpayer': 'TotalImpayer',\n", "        'type': 'Type_Dict'\n", "    }, inplace=True)\n", "    \n", "    df_biens['Type'] = df_biens['Type_Dict'].apply(lambda x: x.get('libelle', 'N/A') if isinstance(x, dict) else 'N/A')\n", "    \n", "    df_proprietaires_locative = explode_column(df_biens, 'Proprietaires', 'Bien')\n", "    df_locataires_locative = explode_column(df_biens, 'Locataires', 'Bien')\n", "    \n", "    print(f\"Biens charges: {len(df_biens)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_locative)}\")\n", "    print(f\"Locataires: {len(df_locataires_locative)}\")\n", "else:\n", "    df_biens = pd.DataFrame()\n", "    df_proprietaires_locative = pd.DataFrame()\n", "    df_locataires_locative = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CHARGEMENT E-SYNDIC ===\n", "Fichier charge: E_syndic.csv\n", "Nombre de lignes: 1\n", "Programmes charges: 3\n", "Proprietaires: 164\n", "Locataires: 51\n", "Incidents: 61\n", "Evenements: 7\n"]}], "source": ["# Chargement E-Syndic\n", "print(\"\\n=== CHARGEMENT E-SYNDIC ===\")\n", "df_esyndic_raw = load_csv_direct('E_syndic.csv')\n", "\n", "if df_esyndic_raw is not None:\n", "    parsed_esyndic = ast.literal_eval(df_esyndic_raw['data'].iloc[0])\n", "    df_programmes = pd.DataFrame(parsed_esyndic)\n", "    \n", "    df_programmes.rename(columns={\n", "        'libelle': 'Programme',\n", "        'pays': 'Pays',\n", "        'ville': 'Ville',\n", "        'superficie': 'Superficie',\n", "        'proprietaire': 'Pro<PERSON>rietaire',\n", "        'locataires': 'Locataires',\n", "        'incidents': 'Incidents',\n", "        'evenements': 'Evenements'\n", "    }, inplace=True)\n", "    \n", "    df_proprietaires_esyndic = explode_column(df_programmes, 'Proprietaire', 'Programme')\n", "    df_locataires_esyndic = explode_column(df_programmes, 'Locataires', 'Programme')\n", "    df_incidents = explode_column(df_programmes, 'Incidents', 'Programme')\n", "    df_evenements = explode_column(df_programmes, 'Evenements', 'Programme')\n", "    \n", "    print(f\"Programmes charges: {len(df_programmes)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_esyndic)}\")\n", "    print(f\"Locataires: {len(df_locataires_esyndic)}\")\n", "    print(f\"Incidents: {len(df_incidents)}\")\n", "    print(f\"Evenements: {len(df_evenements)}\")\n", "else:\n", "    df_programmes = pd.DataFrame()\n", "    df_proprietaires_esyndic = pd.DataFrame()\n", "    df_locataires_esyndic = pd.DataFrame()\n", "    df_incidents = pd.DataFrame()\n", "    df_evenements = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Classe pour les metriques"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["class CombinedMetrics:\n", "    def __init__(self, df_biens, df_programmes, df_prop_loc, df_loc_loc, df_prop_syn, df_loc_syn, df_incidents, df_evenements):\n", "        self.df_biens = df_biens\n", "        self.df_programmes = df_programmes\n", "        self.df_proprietaires_locative = df_prop_loc\n", "        self.df_locataires_locative = df_loc_loc\n", "        self.df_proprietaires_esyndic = df_prop_syn\n", "        self.df_locataires_esyndic = df_loc_syn\n", "        self.df_incidents = df_incidents\n", "        self.df_evenements = df_evenements\n", "    \n", "    def get_fusion_dataframes_programme_locataires(self):\n", "        \"\"\"Fusion des dataframes programme et locataires avec type de bien par programme\"\"\"\n", "        results = []\n", "        \n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                pays = prog_row.get('Pays', 'N/A')\n", "                \n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                \n", "                results.append({\n", "                    'Programme': programme_nom,\n", "                    'Ville': ville,\n", "                    'Pays': pays,\n", "                    'Type_Bien': 'Programme_Immobilier',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Total_Personnes': nb_proprietaires + nb_locataires\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_fusion_proprietaires_biens_locataires(self):\n", "        \"\"\"Dataframe avec chaque proprietaire individuellement : nom, nombre de biens, nombre de locataires\"\"\"\n", "        results = []\n", "        \n", "        # Gestion Locative - par proprietaire\n", "        if not self.df_proprietaires_locative.empty:\n", "            proprietaires_data = {}\n", "            \n", "            for _, prop_row in self.df_proprietaires_locative.iterrows():\n", "                nom_proprietaire = extract_name_from_row(prop_row)\n", "                bien_nom = prop_row['Bien']\n", "                \n", "                if nom_proprietaire not in proprietaires_data:\n", "                    proprietaires_data[nom_proprietaire] = {'biens': set(), 'total_locataires': 0}\n", "                \n", "                proprietaires_data[nom_proprietaire]['biens'].add(bien_nom)\n", "                \n", "                # Compter les locataires de ce bien\n", "                nb_locataires_bien = len(self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom])\n", "                proprietaires_data[nom_proprietaire]['total_locataires'] += nb_locataires_bien\n", "            \n", "            # Ajouter aux resultats\n", "            for nom_prop, data in proprietaires_data.items():\n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Nom_Proprietaire': nom_prop,\n", "                    'Nb_Biens_Possedes': len(data['biens']),\n", "                    'Nb_Locataires_Total': data['total_locataires']\n", "                })\n", "        \n", "        # E-Syndic - par proprietaire\n", "        if not self.df_proprietaires_esyndic.empty:\n", "            proprietaires_data = {}\n", "            \n", "            for _, prop_row in self.df_proprietaires_esyndic.iterrows():\n", "                nom_proprietaire = extract_name_from_row(prop_row)\n", "                programme_nom = prop_row['Programme']\n", "                \n", "                if nom_proprietaire not in proprietaires_data:\n", "                    proprietaires_data[nom_proprietaire] = {'programmes': set(), 'total_locataires': 0}\n", "                \n", "                proprietaires_data[nom_proprietaire]['programmes'].add(programme_nom)\n", "                \n", "                # Compter les locataires de ce programme\n", "                nb_locataires_prog = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                proprietaires_data[nom_proprietaire]['total_locataires'] += nb_locataires_prog\n", "            \n", "            # Ajouter aux resultats\n", "            for nom_prop, data in proprietaires_data.items():\n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Nom_Proprietaire': nom_prop,\n", "                    'Nb_Biens_Possedes': len(data['programmes']),\n", "                    'Nb_Locataires_Total': data['total_locataires']\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_evenements_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Evenements': nb_evenements\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_proprietaires_locataires(self):\n", "        \"\"\"Pour chaque evenement, voir qui l'a organise et faire un total par personne\"\"\"\n", "        results = []\n", "        \n", "        # Parcourir chaque evenement\n", "        if not self.df_evenements.empty:\n", "            # Compter les evenements par personne\n", "            evenements_par_personne = {}\n", "            \n", "            for _, event_row in self.df_evenements.iterrows():\n", "                programme_nom = event_row['Programme']\n", "                \n", "                # Trouver l'organisateur de l'evenement (dans les colonnes de l'evenement)\n", "                organisateur = extract_name_from_row(event_row)\n", "                \n", "                # Determiner si c'est un proprietaire ou locataire\n", "                type_personne = 'Inconnu'\n", "                \n", "                # Verifier si c'est un proprietaire\n", "                proprietaires_prog = self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom]\n", "                for _, prop in proprietaires_prog.iterrows():\n", "                    nom_prop = extract_name_from_row(prop)\n", "                    if nom_prop == organisateur:\n", "                        type_personne = 'Proprietaire'\n", "                        break\n", "                \n", "                # Si pas proprietaire, verifier si c'est un locataire\n", "                if type_personne == 'Inconnu':\n", "                    locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]\n", "                    for _, loc in locataires_prog.iterrows():\n", "                        nom_loc = extract_name_from_row(loc)\n", "                        if nom_loc == organisateur:\n", "                            type_personne = 'Locataire'\n", "                            break\n", "                \n", "                # Compter l'evenement pour cette personne\n", "                key = (organisateur, type_personne)\n", "                if key not in evenements_par_personne:\n", "                    evenements_par_personne[key] = 0\n", "                evenements_par_personne[key] += 1\n", "            \n", "            # Convertir en resultats\n", "            for (nom_personne, type_personne), nb_evenements in evenements_par_personne.items():\n", "                results.append({\n", "                    'Nom_Personne': nom_personne,\n", "                    'Type_Personne': type_personne,\n", "                    'Nb_Evenements_Organises': nb_evenements,\n", "                    'Source': '<PERSON>_Syn<PERSON>'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_incidents_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'incidents par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Incidents': nb_incidents\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def generer_rapport_complet(self):\n", "        print(\"RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\")\n", "        print(\"=\" * 50)\n", "        \n", "        # 1. Fusion dataframes programme et locataires par programme\n", "        print(\"\\n1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\")\n", "        df_fusion_prog = self.get_fusion_dataframes_programme_locataires()\n", "        if not df_fusion_prog.empty:\n", "            display(df_fusion_prog)\n", "            print(f\"Total proprietaires: {df_fusion_prog['Nb_Proprietaires'].sum()}\")\n", "            print(f\"Total locataires: {df_fusion_prog['Nb_Locataires'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee de programmes disponible\")\n", "        \n", "        # 2. Proprietaires avec nombre de biens et locataires\n", "        print(\"\\n2. PROPRIETAIRES AVEC NOMBRE DE BIENS ET LOCATAIRES\")\n", "        df_fusion_prop = self.get_fusion_proprietaires_biens_locataires()\n", "        if not df_fusion_prop.empty:\n", "            display(df_fusion_prop)\n", "            print(f\"Total proprietaires uniques: {len(df_fusion_prop)}\")\n", "        else:\n", "            print(\"Au<PERSON>ne donnee de proprietaires disponible\")\n", "        \n", "        # 3. Nombre d'evenements par programme\n", "        print(\"\\n3. NOMBRE D'EVENEMENTS PAR PROGRAMME\")\n", "        df_events_prog = self.get_nombre_evenements_par_programme()\n", "        if not df_events_prog.empty:\n", "            display(df_events_prog)\n", "            print(f\"Total evenements: {df_events_prog['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements disponible\")\n", "        \n", "        # 4. Evenements organises par personne\n", "        print(\"\\n4. EVENEMENTS ORGANISES PAR PERSONNE\")\n", "        df_events_personnes = self.get_evenements_par_proprietaires_locataires()\n", "        if not df_events_personnes.empty:\n", "            display(df_events_personnes)\n", "            proprietaires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Proprietaire']\n", "            locataires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Locataire']\n", "            print(f\"Evenements organises par proprietaires: {proprietaires_events['Nb_Evenements_Organises'].sum()}\")\n", "            print(f\"Evenements organises par locataires: {locataires_events['Nb_Evenements_Organises'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements par personnes disponible\")\n", "        \n", "        # 5. Nombre d'incidents par programme\n", "        print(\"\\n5. NOMBRE D'INCIDENTS PAR PROGRAMME\")\n", "        df_incidents_prog = self.get_nombre_incidents_par_programme()\n", "        if not df_incidents_prog.empty:\n", "            display(df_incidents_prog)\n", "            print(f\"Total incidents: {df_incidents_prog['Nb_Incidents'].sum()}\")\n", "        else:\n", "            print(\"Au<PERSON>ne donnee d'incidents disponible\")\n", "        \n", "        return {\n", "            'fusion_programmes': df_fusion_prog,\n", "            'fusion_proprietaires': df_fusion_prop,\n", "            'evenements_programmes': df_events_prog,\n", "            'evenements_personnes': df_events_personnes,\n", "            'incidents_programmes': df_incidents_prog\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utilisation"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\n", "==================================================\n", "\n", "1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Pays</th>\n", "      <th>Type_Bien</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "      <th>Total_Personnes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>90</td>\n", "      <td>51</td>\n", "      <td>141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville           Pays  \\\n", "0         CALLISTO ETOILE      Grand-Bassam  Côte d'Ivoire   \n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam  Côte d'Ivoire   \n", "2               SYMPHONIA  Abidjan - Cocody  Côte d'Ivoire   \n", "\n", "              Type_Bien  Nb_Proprietaires  Nb_Locataires  Total_Personnes  \n", "0  Programme_Immobilier                 0              0                0  \n", "1  Programme_Immobilier                74              0               74  \n", "2  Programme_Immobilier                90             51              141  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total proprietaires: 164\n", "Total locataires: 51\n", "\n", "2. PROPRIETAIRES AVEC NOMBRE DE BIENS ET LOCATAIRES\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Source</th>\n", "      <th><PERSON>m_Proprietaire</th>\n", "      <th><PERSON><PERSON>_<PERSON><PERSON>_Possedes</th>\n", "      <th>Nb_Locataires_Total</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>61 65</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>60 45</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>57 61</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>52 56</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>51 56</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>E_Syndic</td>\n", "      <td>BAMBA MASSIAMI</td>\n", "      <td>1</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>E_Syndic</td>\n", "      <td>SCI MA-NELLY</td>\n", "      <td>1</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>E_Syndic</td>\n", "      <td>SCI L'HARMATTAN</td>\n", "      <td>1</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>E_Syndic</td>\n", "      <td>LOGON DIDIER</td>\n", "      <td>1</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>E_Syndic</td>\n", "      <td>KOUAKOU ATCHELAUD LINA MORELLE DESIREE</td>\n", "      <td>1</td>\n", "      <td>51</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>195 rows × 4 columns</p>\n", "</div>"], "text/plain": ["               Source                        Nom_Proprietaire  \\\n", "0    Gestion_Locative                                   61 65   \n", "1    Gestion_Locative                                   60 45   \n", "2    Gestion_Locative                                   57 61   \n", "3    Gestion_Locative                                   52 56   \n", "4    Gestion_Locative                                   51 56   \n", "..                ...                                     ...   \n", "190          E_Syndic                          BAMBA MASSIAMI   \n", "191          E_Syndic                            SCI MA-NELLY   \n", "192          E_Syndic                         SCI L'HARMATTAN   \n", "193          E_Syndic                            LOGON DIDIER   \n", "194          E_Syndic  KOUAKOU ATCHELAUD LINA MORELLE DESIREE   \n", "\n", "     Nb_Biens_Possedes  Nb_Locataires_Total  \n", "0                    1                    0  \n", "1                    1                    0  \n", "2                    1                    0  \n", "3                    1                    0  \n", "4                    1                    0  \n", "..                 ...                  ...  \n", "190                  1                   51  \n", "191                  1                   51  \n", "192                  1                   51  \n", "193                  1                   51  \n", "194                  1                   51  \n", "\n", "[195 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total proprietaires uniques: 195\n", "\n", "3. NOMBRE D'EVENEMENTS PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Nb_Evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville  Nb_Evenements\n", "0         CALLISTO ETOILE      Grand-Bassam              0\n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam              0\n", "2               SYMPHONIA  Abidjan - Cocody              7"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total evenements: 7\n", "\n", "4. EVENEMENTS ORGANISES PAR PERSONNE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON>m_<PERSON>ne</th>\n", "      <th>Type_Personne</th>\n", "      <th>Nb_Evenements_Organises</th>\n", "      <th>Source</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10 Hello world</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9 Anniversaire de ma fille</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8 Test</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7 ANNIVERSAIRE</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6 Anniversaire</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3 ANNIVERSAIRE VILLA 39</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2 Anniversaire enfant malan villa 12</td>\n", "      <td>Inconnu</td>\n", "      <td>1</td>\n", "      <td>E_Syndic</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           Nom_Personne Type_Personne  \\\n", "0                        10 Hello world       Inconnu   \n", "1            9 Anniversaire de ma fille       Inconnu   \n", "2                                8 Test       Inconnu   \n", "3                        7 ANNIVERSAIRE       Inconnu   \n", "4                        6 Anniversaire       Inconnu   \n", "5               3 ANNIVERSAIRE VILLA 39       Inconnu   \n", "6  2 Anniversaire enfant malan villa 12       Inconnu   \n", "\n", "   Nb_Evenements_Organises    Source  \n", "0                        1  E_Syndic  \n", "1                        1  E_Syndic  \n", "2                        1  E_Syndic  \n", "3                        1  E_Syndic  \n", "4                        1  E_Syndic  \n", "5                        1  E_Syndic  \n", "6                        1  E_Syndic  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evenements organises par proprietaires: 0\n", "Evenements organises par locataires: 0\n", "\n", "5. NOMBRE D'INCIDENTS PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Nb_Incidents</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>61</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville  Nb_Incidents\n", "0         CALLISTO ETOILE      Grand-Bassam             0\n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam             0\n", "2               SYMPHONIA  Abidjan - Cocody            61"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total incidents: 61\n"]}], "source": ["# Creer l'instance de la classe\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes, \n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# Generer le rapport complet\n", "resultats = metrics.generer_rapport_complet()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}