{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE DES DONNÉES G-STOCK APPROVISIONNEMENT\n", "## Liste des imports utiles"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: g_stockapprovisionnement.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>message</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>success</td>\n", "      <td>Données d'approvisionnement récupérées</td>\n", "      <td>{'id': 1, 'title': 'G-Stock Approvisionnement'...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                 message  \\\n", "0  success  Données d'approvisionnement récupérées   \n", "\n", "                                                data  \n", "0  {'id': 1, 'title': 'G-Stock Approvisionnement'...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = 'g_stockapprovisionnement.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")\n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parsing des données"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsing réussi\n", "\n", "Structure du champ 'data':\n", "<class 'dict'>\n", "Clés principales: ['id', 'title', 'description', 'data', 'created_at', 'updated_at']\n"]}], "source": ["def parse_json_data(json_str):\n", "    \"\"\"Parse une chaîne JSON en dictionnaire Python\"\"\"\n", "    try:\n", "        parsed = ast.literal_eval(json_str)\n", "        print(\"Parsing réussi\")\n", "        return parsed\n", "    except:\n", "        print(\"Erreur de parsing, retour de la chaîne originale\")\n", "        return json_str\n", "\n", "# Test du parsing sur le premier enregistrement\n", "if not df_raw.empty and 'data' in df_raw.columns:\n", "    sample_data = parse_json_data(df_raw['data'].iloc[0])\n", "    print(\"\\nStructure du champ 'data':\")\n", "    print(type(sample_data))\n", "    if isinstance(sample_data, dict):\n", "        print(\"Clés principales:\", list(sample_data.keys()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Création du dataframe parent"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame parent créé avec 1 enregistrements\n", "\n", "Aperçu du DataFrame parent:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>message</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>success</td>\n", "      <td>Données d'approvisionnement récupérées</td>\n", "      <td>{'id': 1, 'title': 'G-Stock Approvisionnement'...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                 message  \\\n", "0  success  Données d'approvisionnement récupérées   \n", "\n", "                                                data  \n", "0  {'id': 1, 'title': 'G-Stock Approvisionnement'...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def create_parent_dataframe(df_raw):\n", "    \"\"\"Crée le DataFrame parent des approvisionnements\"\"\"\n", "    df = df_raw.copy()\n", "    print(f\"DataFrame parent créé avec {len(df)} enregistrements\")\n", "    return df\n", "\n", "# Création du DataFrame parent\n", "df_approvisionnements = create_parent_dataframe(df_raw)\n", "print(\"\\nAperçu du DataFrame parent:\")\n", "display(df_approvisionnements.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Nettoyage des données"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nettoyage des données terminé\n", "\n", "Aperçu des données nettoyées:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>message</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Success</td>\n", "      <td>Données D'Approvisionnement Récupérées</td>\n", "      <td>{'id': 1, 'title': 'G-Stock Approvisionnement'...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                 message  \\\n", "0  Success  Données D'Approvisionnement Récupérées   \n", "\n", "                                                data  \n", "0  {'id': 1, 'title': 'G-Stock Approvisionnement'...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def clean_data(df):\n", "    \"\"\"Nettoie les données en appliquant la capitalisation\"\"\"\n", "    df_clean = df.copy()\n", "    \n", "    # Capitalisation des champs texte\n", "    text_columns = ['status', 'message']\n", "    \n", "    for col in text_columns:\n", "        if col in df_clean.columns:\n", "            df_clean[col] = df_clean[col].astype(str).str.title()\n", "    \n", "    print(\"Nettoyage des données terminé\")\n", "    return df_clean\n", "\n", "# Application du nettoyage\n", "df_approvisionnements_clean = clean_data(df_approvisionnements)\n", "print(\"\\nAperçu des données nettoyées:\")\n", "display(df_approvisionnements_clean.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Éclatement des données imbriquées"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Données éclatées:\n", "- Programmes: 4 enregistrements\n", "- Approvisionnements: 447 enregistrements\n", "- Items: 1345 enregistrements\n", "- Produits: 1345 enregistrements\n", "\n", "Aperçu des DataFrames éclatés:\n", "\n", "1. Programmes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme_id</th>\n", "      <th>programme_libelle</th>\n", "      <th>programme_nom</th>\n", "      <th>nb_approvisionnements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>K2</td>\n", "      <td>89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8</td>\n", "      <td>CALLISTO SHOPPING PHASE 2</td>\n", "      <td>CALLISTO SHOPPING PHASE 2</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>LES RESIDENCES BO'REFLETS</td>\n", "      <td>LES RESIDENCES BO'REFLETS</td>\n", "      <td>272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   programme_id          programme_libelle              programme_nom  \\\n", "0             7                         K2                         K2   \n", "1             8  CALLISTO SHOPPING PHASE 2  CALLISTO SHOPPING PHASE 2   \n", "2             1  LES RESIDENCES BO'REFLETS  LES RESIDENCES BO'REFLETS   \n", "3             2     LES RÉSIDENCES KOTIBES     LES RÉSIDENCES KOTIBES   \n", "\n", "   nb_approvisionnements  \n", "0                     89  \n", "1                     33  \n", "2                    272  \n", "3                     53  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "2. Approvisionnements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>approvisionnement_id</th>\n", "      <th>programme_id</th>\n", "      <th>programme_nom</th>\n", "      <th>fournisseur_id</th>\n", "      <th>ref</th>\n", "      <th>created_at</th>\n", "      <th>nb_items</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1073</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>494</td>\n", "      <td>BR2507-1073</td>\n", "      <td>2025-07-08T12:32:21.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1072</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>164</td>\n", "      <td>BR2507-1072</td>\n", "      <td>2025-07-07T11:17:55.000000Z</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1069</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>1059</td>\n", "      <td>BR2507-1069</td>\n", "      <td>2025-07-05T11:16:54.000000Z</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1005</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>903</td>\n", "      <td>BR2506-1005</td>\n", "      <td>2025-06-12T12:52:53.000000Z</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1003</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>1530</td>\n", "      <td>BR2506-1003</td>\n", "      <td>2025-06-04T14:14:19.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   approvisionnement_id  programme_id programme_nom  fournisseur_id  \\\n", "0                  1073             7            K2             494   \n", "1                  1072             7            K2             164   \n", "2                  1069             7            K2            1059   \n", "3                  1005             7            K2             903   \n", "4                  1003             7            K2            1530   \n", "\n", "           ref                   created_at  nb_items  \n", "0  BR2507-1073  2025-07-08T12:32:21.000000Z         1  \n", "1  BR2507-1072  2025-07-07T11:17:55.000000Z         5  \n", "2  BR2507-1069  2025-07-05T11:16:54.000000Z         9  \n", "3  BR2506-1005  2025-06-12T12:52:53.000000Z         6  \n", "4  BR2506-1003  2025-06-04T14:14:19.000000Z         1  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "3. Items:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>approvisionnement_id</th>\n", "      <th>programme_nom</th>\n", "      <th>stock_fournisseur_id</th>\n", "      <th>produit_id</th>\n", "      <th>quantite</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2680</td>\n", "      <td>1073</td>\n", "      <td>K2</td>\n", "      <td>1073</td>\n", "      <td>125</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2675</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1958</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2676</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1957</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2677</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1959</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2678</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1974</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   item_id  approvisionnement_id programme_nom  stock_fournisseur_id  \\\n", "0     2680                  1073            K2                  1073   \n", "1     2675                  1072            K2                  1072   \n", "2     2676                  1072            K2                  1072   \n", "3     2677                  1072            K2                  1072   \n", "4     2678                  1072            K2                  1072   \n", "\n", "   produit_id  quantite  \n", "0         125      24.0  \n", "1        1958       1.0  \n", "2        1957       2.0  \n", "3        1959       1.0  \n", "4        1974       1.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "4. Produits:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>produit_id</th>\n", "      <th>item_id</th>\n", "      <th>programme_nom</th>\n", "      <th>produit_code</th>\n", "      <th>produit_libelle</th>\n", "      <th>quantite_item</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>125</td>\n", "      <td>2680</td>\n", "      <td>K2</td>\n", "      <td>0x7c</td>\n", "      <td><PERSON><PERSON> lagunaire</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1958</td>\n", "      <td>2675</td>\n", "      <td>K2</td>\n", "      <td>ATAEREH</td>\n", "      <td>PIED DE BICHE 100CM</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1957</td>\n", "      <td>2676</td>\n", "      <td>K2</td>\n", "      <td>ATRSHAQ</td>\n", "      <td>NIVEAU TRAPEZOIDAL MLH 80CM</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1959</td>\n", "      <td>2677</td>\n", "      <td>K2</td>\n", "      <td>ATGYOKM</td>\n", "      <td>SCIE EGOINE MCH.JETCUT SP380 215281</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1974</td>\n", "      <td>2678</td>\n", "      <td>K2</td>\n", "      <td>ATYRU9T</td>\n", "      <td>CORDEAU MACON 30M Ø1,5MM ET POUDRE DE 140G</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   produit_id  item_id programme_nom produit_code  \\\n", "0         125     2680            K2         0x7c   \n", "1        1958     2675            K2      ATAEREH   \n", "2        1957     2676            K2      ATRSHAQ   \n", "3        1959     2677            K2      ATGYOKM   \n", "4        1974     2678            K2      ATYRU9T   \n", "\n", "                              produit_libelle  quantite_item  \n", "0                             Sable lagunaire           24.0  \n", "1                         PIED DE BICHE 100CM            1.0  \n", "2                 NIVEAU TRAPEZOIDAL MLH 80CM            2.0  \n", "3         SCIE EGOINE MCH.JETCUT SP380 215281            1.0  \n", "4  CORDEAU MACON 30M Ø1,5MM ET POUDRE DE 140G            1.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def explode_data_field(df):\n", "    \"\"\"Éclate le champ 'data' qui contient la structure complexe des approvisionnements\"\"\"\n", "    if 'data' not in df.columns:\n", "        print(\"Pas de champ 'data' à éclater\")\n", "        return df, pd.DataFrame(), pd.DataFrame(), pd.DataFrame()\n", "    \n", "    programmes_records = []\n", "    approvisionnements_records = []\n", "    items_records = []\n", "    produits_records = []\n", "    \n", "    for _, row in df.iterrows():\n", "        try:\n", "            # Parse le JSON du champ data\n", "            if isinstance(row['data'], str):\n", "                data_details = ast.literal_eval(row['data'])\n", "            else:\n", "                data_details = row['data']\n", "            \n", "            if isinstance(data_details, dict) and 'data' in data_details:\n", "                stocks_data = data_details['data']\n", "                \n", "                if 'stocks' in stocks_data:\n", "                    for stock in stocks_data['stocks']:\n", "                        # 1. <PERSON><PERSON><PERSON> du programme\n", "                        programme = stock.get('programme', {})\n", "                        programme_record = {\n", "                            'programme_id': programme.get('id'),\n", "                            'programme_libelle': programme.get('libelle', ''),\n", "                            'programme_nom': programme.get('nom', ''),\n", "                            'nb_approvisionnements': len(stock.get('approvisionnements', []))\n", "                        }\n", "                        programmes_records.append(programme_record)\n", "                        \n", "                        # 2. Données des approvisionnements\n", "                        for appro in stock.get('approvisionnements', []):\n", "                            appro_record = {\n", "                                'approvisionnement_id': appro.get('id'),\n", "                                'programme_id': appro.get('programme_id'),\n", "                                'programme_nom': programme.get('nom', ''),\n", "                                'fournisseur_id': appro.get('fournisseur_id'),\n", "                                'ref': appro.get('ref', ''),\n", "                                'created_at': appro.get('created_at', ''),\n", "                                'nb_items': len(appro.get('items', []))\n", "                            }\n", "                            approvisionnements_records.append(appro_record)\n", "                            \n", "                            # 3. <PERSON>n<PERSON> des items\n", "                            for item in appro.get('items', []):\n", "                                item_record = {\n", "                                    'item_id': item.get('id'),\n", "                                    'approvisionnement_id': appro.get('id'),\n", "                                    'programme_nom': programme.get('nom', ''),\n", "                                    'stock_fournisseur_id': item.get('stock_fournisseur_id'),\n", "                                    'produit_id': item.get('produit_id'),\n", "                                    'quantite': item.get('qte', 0)\n", "                                }\n", "                                items_records.append(item_record)\n", "                                \n", "                                # 4. Donn<PERSON> des produits\n", "                                produit = item.get('produits', {})\n", "                                if produit:\n", "                                    produit_record = {\n", "                                        'produit_id': produit.get('id'),\n", "                                        'item_id': item.get('id'),\n", "                                        'programme_nom': programme.get('nom', ''),\n", "                                        'produit_code': produit.get('code', ''),\n", "                                        'produit_libelle': produit.get('libelle', ''),\n", "                                        'quantite_item': item.get('qte', 0)\n", "                                    }\n", "                                    produits_records.append(produit_record)\n", "                                    \n", "        except Exception as e:\n", "            print(f\"Erreur parsing data: {e}\")\n", "    \n", "    # Création des DataFrames\n", "    df_programmes = pd.DataFrame(programmes_records).drop_duplicates(subset=['programme_id'])\n", "    df_approvisionnements = pd.DataFrame(approvisionnements_records)\n", "    df_items = pd.DataFrame(items_records)\n", "    df_produits = pd.DataFrame(produits_records)\n", "    \n", "    print(f\"Données éclatées:\")\n", "    print(f\"- Programmes: {len(df_programmes)} enregistrements\")\n", "    print(f\"- Approvisionnements: {len(df_approvisionnements)} enregistrements\")\n", "    print(f\"- Items: {len(df_items)} enregistrements\")\n", "    print(f\"- Produits: {len(df_produits)} enregistrements\")\n", "    \n", "    return df_programmes, df_approvisionnements, df_items, df_produits\n", "\n", "# Éclatement des données\n", "df_programmes, df_approvisionnements, df_items, df_produits = explode_data_field(df_approvisionnements_clean)\n", "\n", "print(\"\\nAperçu des DataFrames éclatés:\")\n", "print(\"\\n1. Programmes:\")\n", "display(df_programmes.head())\n", "print(\"\\n2. Approvisionnements:\")\n", "display(df_approvisionnements.head())\n", "print(\"\\n3. Items:\")\n", "display(df_items.head())\n", "print(\"\\n4. Produits:\")\n", "display(df_produits.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyses et statistiques"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANALYSES DES APPROVISIONNEMENTS ===\n", "Nombre de programmes: 4\n", "\n", "Top 5 programmes par nombre d'approvisionnements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme_nom</th>\n", "      <th>nb_approvisionnements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>LES RESIDENCES BO'REFLETS</td>\n", "      <td>272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>K2</td>\n", "      <td>89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO SHOPPING PHASE 2</td>\n", "      <td>33</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               programme_nom  nb_approvisionnements\n", "2  LES RESIDENCES BO'REFLETS                    272\n", "0                         K2                     89\n", "3     LES RÉSIDENCES KOTIBES                     53\n", "1  CALLISTO SHOPPING PHASE 2                     33"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Nombre total d'approvisionnements: 447\n", "\n", "Top 5 programmes par approvisionnements:\n"]}, {"data": {"text/plain": ["programme_nom\n", "LES RESIDENCES BO'REFLETS    272\n", "K2                            89\n", "LES RÉSIDENCES KOTIBES        53\n", "CALLISTO SHOPPING PHASE 2     33\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Nombre total d'items: 1345\n", "Quantité totale: 885,959\n", "Quantité moyenne par item: 658.71\n", "\n", "Nombre de produits uniques: 363\n", "\n", "Top 5 produits les plus approvisionnés:\n"]}, {"data": {"text/plain": ["produit_libelle\n", "Agglos 15 creux (15x20x50)      436586.0\n", "Tuile D.R terracota prestige     95550.0\n", "Agglos 15 plein (15x20x50)       81035.0\n", "Hourdis 16 (20x53x16)            61589.0\n", "Agglos 10 creux (10x20x50)       15410.0\n", "Name: quantite_item, dtype: float64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def analyze_approvisionnements(df_prog, df_appro, df_items, df_prod):\n", "    \"\"\"Effectue des analyses sur les données d'approvisionnement\"\"\"\n", "    print(\"=== ANALYSES DES APPROVISIONNEMENTS ===\")\n", "    \n", "    # Analyses des programmes\n", "    if not df_prog.empty:\n", "        print(f\"Nombre de programmes: {len(df_prog)}\")\n", "        print(\"\\nTop 5 programmes par nombre d'approvisionnements:\")\n", "        top_programmes = df_prog.nlargest(5, 'nb_approvisionnements')[['programme_nom', 'nb_approvisionnements']]\n", "        display(top_programmes)\n", "    \n", "    # Analyses des approvisionnements\n", "    if not df_appro.empty:\n", "        print(f\"\\nNombre total d'approvisionnements: {len(df_appro)}\")\n", "        print(\"\\nTop 5 programmes par approvisionnements:\")\n", "        top_appro_prog = df_appro['programme_nom'].value_counts().head()\n", "        display(top_appro_prog)\n", "    \n", "    # Analyses des items\n", "    if not df_items.empty:\n", "        print(f\"\\nNombre total d'items: {len(df_items)}\")\n", "        print(f\"Quantité totale: {df_items['quantite'].sum():,.0f}\")\n", "        print(f\"Quantité moyenne par item: {df_items['quantite'].mean():.2f}\")\n", "    \n", "    # Analyses des produits\n", "    if not df_prod.empty:\n", "        print(f\"\\nNombre de produits uniques: {df_prod['produit_id'].nunique()}\")\n", "        print(\"\\nTop 5 produits les plus approvisionnés:\")\n", "        top_produits = df_prod.groupby('produit_libelle')['quantite_item'].sum().nlargest(5)\n", "        display(top_produits)\n", "\n", "# Analyses des données\n", "analyze_approvisionnements(df_programmes, df_approvisionnements, df_items, df_produits)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage des DataFrames finaux"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAMES FINAUX ===\n", "\n", "1. DataFrame des programmes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme_id</th>\n", "      <th>programme_libelle</th>\n", "      <th>programme_nom</th>\n", "      <th>nb_approvisionnements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>K2</td>\n", "      <td>89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8</td>\n", "      <td>CALLISTO SHOPPING PHASE 2</td>\n", "      <td>CALLISTO SHOPPING PHASE 2</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>LES RESIDENCES BO'REFLETS</td>\n", "      <td>LES RESIDENCES BO'REFLETS</td>\n", "      <td>272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   programme_id          programme_libelle              programme_nom  \\\n", "0             7                         K2                         K2   \n", "1             8  CALLISTO SHOPPING PHASE 2  CALLISTO SHOPPING PHASE 2   \n", "2             1  LES RESIDENCES BO'REFLETS  LES RESIDENCES BO'REFLETS   \n", "3             2     LES RÉSIDENCES KOTIBES     LES RÉSIDENCES KOTIBES   \n", "\n", "   nb_approvisionnements  \n", "0                     89  \n", "1                     33  \n", "2                    272  \n", "3                     53  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "2. DataFrame des approvisionnements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>approvisionnement_id</th>\n", "      <th>programme_id</th>\n", "      <th>programme_nom</th>\n", "      <th>fournisseur_id</th>\n", "      <th>ref</th>\n", "      <th>created_at</th>\n", "      <th>nb_items</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1073</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>494</td>\n", "      <td>BR2507-1073</td>\n", "      <td>2025-07-08T12:32:21.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1072</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>164</td>\n", "      <td>BR2507-1072</td>\n", "      <td>2025-07-07T11:17:55.000000Z</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1069</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>1059</td>\n", "      <td>BR2507-1069</td>\n", "      <td>2025-07-05T11:16:54.000000Z</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1005</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>903</td>\n", "      <td>BR2506-1005</td>\n", "      <td>2025-06-12T12:52:53.000000Z</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1003</td>\n", "      <td>7</td>\n", "      <td>K2</td>\n", "      <td>1530</td>\n", "      <td>BR2506-1003</td>\n", "      <td>2025-06-04T14:14:19.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>442</th>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>25</td>\n", "      <td>BR2403-0053</td>\n", "      <td>2024-03-26T16:57:44.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>443</th>\n", "      <td>52</td>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>22</td>\n", "      <td>BR2403-0052</td>\n", "      <td>2024-03-25T17:51:04.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>444</th>\n", "      <td>51</td>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>22</td>\n", "      <td>BR2403-0051</td>\n", "      <td>2024-03-25T16:17:58.000000Z</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>445</th>\n", "      <td>50</td>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>34</td>\n", "      <td>BR2403-0050</td>\n", "      <td>2024-03-22T16:29:19.000000Z</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>446</th>\n", "      <td>49</td>\n", "      <td>2</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>34</td>\n", "      <td>BR2403-0049</td>\n", "      <td>2024-03-22T16:25:14.000000Z</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>447 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     approvisionnement_id  programme_id           programme_nom  \\\n", "0                    1073             7                      K2   \n", "1                    1072             7                      K2   \n", "2                    1069             7                      K2   \n", "3                    1005             7                      K2   \n", "4                    1003             7                      K2   \n", "..                    ...           ...                     ...   \n", "442                    53             2  LES RÉSIDENCES KOTIBES   \n", "443                    52             2  LES RÉSIDENCES KOTIBES   \n", "444                    51             2  LES RÉSIDENCES KOTIBES   \n", "445                    50             2  LES RÉSIDENCES KOTIBES   \n", "446                    49             2  LES RÉSIDENCES KOTIBES   \n", "\n", "     fournisseur_id          ref                   created_at  nb_items  \n", "0               494  BR2507-1073  2025-07-08T12:32:21.000000Z         1  \n", "1               164  BR2507-1072  2025-07-07T11:17:55.000000Z         5  \n", "2              1059  BR2507-1069  2025-07-05T11:16:54.000000Z         9  \n", "3               903  BR2506-1005  2025-06-12T12:52:53.000000Z         6  \n", "4              1530  BR2506-1003  2025-06-04T14:14:19.000000Z         1  \n", "..              ...          ...                          ...       ...  \n", "442              25  BR2403-0053  2024-03-26T16:57:44.000000Z         1  \n", "443              22  BR2403-0052  2024-03-25T17:51:04.000000Z         1  \n", "444              22  BR2403-0051  2024-03-25T16:17:58.000000Z         1  \n", "445              34  BR2403-0050  2024-03-22T16:29:19.000000Z         4  \n", "446              34  BR2403-0049  2024-03-22T16:25:14.000000Z        12  \n", "\n", "[447 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "3. DataFrame des items:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>approvisionnement_id</th>\n", "      <th>programme_nom</th>\n", "      <th>stock_fournisseur_id</th>\n", "      <th>produit_id</th>\n", "      <th>quantite</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2680</td>\n", "      <td>1073</td>\n", "      <td>K2</td>\n", "      <td>1073</td>\n", "      <td>125</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2675</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1958</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2676</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1957</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2677</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1959</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2678</td>\n", "      <td>1072</td>\n", "      <td>K2</td>\n", "      <td>1072</td>\n", "      <td>1974</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1340</th>\n", "      <td>194</td>\n", "      <td>49</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>49</td>\n", "      <td>258</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1341</th>\n", "      <td>195</td>\n", "      <td>49</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>49</td>\n", "      <td>237</td>\n", "      <td>72.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1342</th>\n", "      <td>196</td>\n", "      <td>49</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>49</td>\n", "      <td>236</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1343</th>\n", "      <td>197</td>\n", "      <td>49</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>49</td>\n", "      <td>205</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1344</th>\n", "      <td>198</td>\n", "      <td>49</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>49</td>\n", "      <td>227</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1345 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      item_id  approvisionnement_id           programme_nom  \\\n", "0        2680                  1073                      K2   \n", "1        2675                  1072                      K2   \n", "2        2676                  1072                      K2   \n", "3        2677                  1072                      K2   \n", "4        2678                  1072                      K2   \n", "...       ...                   ...                     ...   \n", "1340      194                    49  LES RÉSIDENCES KOTIBES   \n", "1341      195                    49  LES RÉSIDENCES KOTIBES   \n", "1342      196                    49  LES RÉSIDENCES KOTIBES   \n", "1343      197                    49  LES RÉSIDENCES KOTIBES   \n", "1344      198                    49  LES RÉSIDENCES KOTIBES   \n", "\n", "      stock_fournisseur_id  produit_id  quantite  \n", "0                     1073         125      24.0  \n", "1                     1072        1958       1.0  \n", "2                     1072        1957       2.0  \n", "3                     1072        1959       1.0  \n", "4                     1072        1974       1.0  \n", "...                    ...         ...       ...  \n", "1340                    49         258      24.0  \n", "1341                    49         237      72.0  \n", "1342                    49         236      24.0  \n", "1343                    49         205      36.0  \n", "1344                    49         227      36.0  \n", "\n", "[1345 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "4. DataFrame des produits:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>produit_id</th>\n", "      <th>item_id</th>\n", "      <th>programme_nom</th>\n", "      <th>produit_code</th>\n", "      <th>produit_libelle</th>\n", "      <th>quantite_item</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>125</td>\n", "      <td>2680</td>\n", "      <td>K2</td>\n", "      <td>0x7c</td>\n", "      <td><PERSON><PERSON> lagunaire</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1958</td>\n", "      <td>2675</td>\n", "      <td>K2</td>\n", "      <td>ATAEREH</td>\n", "      <td>PIED DE BICHE 100CM</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1957</td>\n", "      <td>2676</td>\n", "      <td>K2</td>\n", "      <td>ATRSHAQ</td>\n", "      <td>NIVEAU TRAPEZOIDAL MLH 80CM</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1959</td>\n", "      <td>2677</td>\n", "      <td>K2</td>\n", "      <td>ATGYOKM</td>\n", "      <td>SCIE EGOINE MCH.JETCUT SP380 215281</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1974</td>\n", "      <td>2678</td>\n", "      <td>K2</td>\n", "      <td>ATYRU9T</td>\n", "      <td>CORDEAU MACON 30M Ø1,5MM ET POUDRE DE 140G</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1340</th>\n", "      <td>258</td>\n", "      <td>194</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>CD002</td>\n", "      <td>Coude 1/4 PVC DIAM 110 - 1 mm</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1341</th>\n", "      <td>237</td>\n", "      <td>195</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>0xn22</td>\n", "      <td>Reduction PVC DIAM 75/40</td>\n", "      <td>72.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1342</th>\n", "      <td>236</td>\n", "      <td>196</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>0xn21</td>\n", "      <td>Reduction PVC DIAM 40/32</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1343</th>\n", "      <td>205</td>\n", "      <td>197</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>0x1A</td>\n", "      <td><PERSON><PERSON> tangit</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1344</th>\n", "      <td>227</td>\n", "      <td>198</td>\n", "      <td>LES RÉSIDENCES KOTIBES</td>\n", "      <td>0x5gc</td>\n", "      <td>Gaz camping</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1345 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      produit_id  item_id           programme_nom produit_code  \\\n", "0            125     2680                      K2         0x7c   \n", "1           1958     2675                      K2      ATAEREH   \n", "2           1957     2676                      K2      ATRSHAQ   \n", "3           1959     2677                      K2      ATGYOKM   \n", "4           1974     2678                      K2      ATYRU9T   \n", "...          ...      ...                     ...          ...   \n", "1340         258      194  LES RÉSIDENCES KOTIBES        CD002   \n", "1341         237      195  LES RÉSIDENCES KOTIBES        0xn22   \n", "1342         236      196  LES RÉSIDENCES KOTIBES        0xn21   \n", "1343         205      197  LES RÉSIDENCES KOTIBES         0x1A   \n", "1344         227      198  LES RÉSIDENCES KOTIBES        0x5gc   \n", "\n", "                                 produit_libelle  quantite_item  \n", "0                                Sable lagunaire           24.0  \n", "1                            PIED DE BICHE 100CM            1.0  \n", "2                    NIVEAU TRAPEZOIDAL MLH 80CM            2.0  \n", "3            SCIE EGOINE MCH.JETCUT SP380 215281            1.0  \n", "4     CORDEAU MACON 30M Ø1,5MM ET POUDRE DE 140G            1.0  \n", "...                                          ...            ...  \n", "1340               Coude 1/4 PVC DIAM 110 - 1 mm           24.0  \n", "1341                    Reduction PVC DIAM 75/40           72.0  \n", "1342                    Reduction PVC DIAM 40/32           24.0  \n", "1343                                <PERSON>le tangit           36.0  \n", "1344                                 Gaz camping           36.0  \n", "\n", "[1345 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Nombre total de DataFrames: 4\n", "Structure hiérarchique: Programmes → Approvisionnements → Items → Produits\n"]}], "source": ["print(\"=== DATAFRAMES FINAUX ===\")\n", "\n", "print(\"\\n1. DataFrame des programmes:\")\n", "display(df_programmes)\n", "\n", "print(\"\\n2. DataFrame des approvisionnements:\")\n", "display(df_approvisionnements)\n", "\n", "print(\"\\n3. DataFrame des items:\")\n", "display(df_items)\n", "\n", "print(\"\\n4. DataFrame des produits:\")\n", "display(df_produits)\n", "\n", "print(f\"\\nNombre total de DataFrames: 4\")\n", "print(f\"Structure hiérarchique: Programmes → Approvisionnements → Items → Produits\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}