import pandas as pd
import json
import ast

csv_file = 'sentinelle_data.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

def parse_json_data(json_str):
    parsed = ast.literal_eval(json_str)
    print("Parsing réussi")
    return parsed
#Parsing des donnees
parsed_data = parse_json_data(df_raw['data'].iloc[0])

df_sentinelle = pd.DataFrame(parsed_data)