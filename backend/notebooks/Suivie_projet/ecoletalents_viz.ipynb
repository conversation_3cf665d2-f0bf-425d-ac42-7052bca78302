{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE DES DONNÉES ÉCOLE DES TALENTS\n", "## Liste des imports utiles"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: ecole_talents_data.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>message</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>success</td>\n", "      <td>Données Ecole des Talents récupérées et sauveg...</td>\n", "      <td>{'id': 1, 'title': 'Ecole des Talents Data Imp...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                            message  \\\n", "0  success  Données Ecole des Talents récupérées et sauveg...   \n", "\n", "                                                data  \n", "0  {'id': 1, 'title': 'Ecole des Talents Data Imp...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = 'ecole_talents_data.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")\n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parsing des données"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsing réussi\n"]}], "source": ["def parse_json_data(json_str):\n", "    parsed = ast.literal_eval(json_str)\n", "    print(\"Parsing réussi\")\n", "    return parsed\n", "\n", "# Parsing des données\n", "parsed_data = parse_json_data(df_raw['data'].iloc[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Création du dataframe parent"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame créé avec 2 promotions\n", "\n", "Colonnes disponibles:\n", "['id', 'name', 'total_general', 'total_eligible', 'total_noneligible', 'inscription_par_metier', 'presence_par_metier', 'presence_par_chantier', 'presence_par_genre', 'appreciation_par_metier']\n", "\n", " Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>total_general</th>\n", "      <th>total_eligible</th>\n", "      <th>total_noneligible</th>\n", "      <th>inscription_par_metier</th>\n", "      <th>presence_par_metier</th>\n", "      <th>presence_par_chantier</th>\n", "      <th>presence_par_genre</th>\n", "      <th>appreciation_par_metier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>PROMOTION 2023</td>\n", "      <td>1210</td>\n", "      <td>432</td>\n", "      <td>778</td>\n", "      <td>[{'metier': 'Maçon', 'total': 219, 'eligible':...</td>\n", "      <td>{'Maçon': {'travail': 0, 'absence': 0, 'retard...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'Maçon': {'Capable': 0, 'Autonome': 0, 'Initi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>PROMOTION 2024</td>\n", "      <td>3746</td>\n", "      <td>2557</td>\n", "      <td>1189</td>\n", "      <td>[{'metier': 'MAÇON', 'total': 269, 'eligible':...</td>\n", "      <td>{'MAÇON': {'travail': 0, 'absence': 0, 'retard...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'MAÇON': {'Capable': 0, 'Autonome': 0, 'Initi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id            name  total_general  total_eligible  total_noneligible  \\\n", "0   1  PROMOTION 2023           1210             432                778   \n", "1   2  PROMOTION 2024           3746            2557               1189   \n", "\n", "                              inscription_par_metier  \\\n", "0  [{'metier': 'Maçon', 'total': 219, 'eligible':...   \n", "1  [{'metier': 'MAÇON', 'total': 269, 'eligible':...   \n", "\n", "                                 presence_par_metier presence_par_chantier  \\\n", "0  {'Maçon': {'travail': 0, 'absence': 0, 'retard...                    []   \n", "1  {'MAÇON': {'travail': 0, 'absence': 0, 'retard...                    []   \n", "\n", "  presence_par_genre                            appreciation_par_metier  \n", "0                 []  {'Maçon': {'Capable': 0, 'Autonome': 0, 'Initi...  \n", "1                 []  {'MAÇON': {'Capable': 0, 'Autonome': 0, 'Initi...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Extraction des données des promotions\n", "promotions_data = parsed_data['data']['data']\n", "df_promotions = pd.DataFrame(promotions_data)\n", "\n", "print(f\"DataFrame créé avec {len(df_promotions)} promotions\")\n", "\n", "# Affichage des colonnes disponibles\n", "print(\"\\nColonnes disponibles:\")\n", "print(df_promotions.columns.tolist())\n", "\n", "# Affichage des données de base\n", "print(\"\\n Aperçu des données:\")\n", "display(df_promotions.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>Promotion</th>\n", "      <th>Total_General</th>\n", "      <th>Total_Eligible</th>\n", "      <th>Total_NonEligible</th>\n", "      <th>Inscriptions_Metier</th>\n", "      <th>Presence_Metier</th>\n", "      <th>presence_par_chantier</th>\n", "      <th>presence_par_genre</th>\n", "      <th>Appreciation_Metier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>PROMOTION 2023</td>\n", "      <td>1210</td>\n", "      <td>432</td>\n", "      <td>778</td>\n", "      <td>[{'metier': 'Maçon', 'total': 219, 'eligible':...</td>\n", "      <td>{'Maçon': {'travail': 0, 'absence': 0, 'retard...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'Maçon': {'Capable': 0, 'Autonome': 0, 'Initi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>PROMOTION 2024</td>\n", "      <td>3746</td>\n", "      <td>2557</td>\n", "      <td>1189</td>\n", "      <td>[{'metier': 'MAÇON', 'total': 269, 'eligible':...</td>\n", "      <td>{'MAÇON': {'travail': 0, 'absence': 0, 'retard...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'MAÇON': {'Capable': 0, 'Autonome': 0, 'Initi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id       Promotion  Total_General  Total_Eligible  Total_NonEligible  \\\n", "0   1  PROMOTION 2023           1210             432                778   \n", "1   2  PROMOTION 2024           3746            2557               1189   \n", "\n", "                                 Inscriptions_Metier  \\\n", "0  [{'metier': 'Maçon', 'total': 219, 'eligible':...   \n", "1  [{'metier': 'MAÇON', 'total': 269, 'eligible':...   \n", "\n", "                                     Presence_Metier presence_par_chantier  \\\n", "0  {'Maçon': {'travail': 0, 'absence': 0, 'retard...                    []   \n", "1  {'MAÇON': {'travail': 0, 'absence': 0, 'retard...                    []   \n", "\n", "  presence_par_genre                                Appreciation_Metier  \n", "0                 []  {'Maçon': {'Capable': 0, 'Autonome': 0, 'Initi...  \n", "1                 []  {'MAÇON': {'Capable': 0, 'Autonome': 0, 'Initi...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["class Cleaning:\n", "    def __init__(self, df):\n", "        self.df = df.copy()\n", "\n", "    def clean(self):\n", "        # Renommer colonnes\n", "        self.df.rename(columns={\n", "            'name': 'Promotion',\n", "            'total_general': 'Total_General',\n", "            'total_eligible': 'Total_Eligible',\n", "            'total_noneligible': 'Total_NonEligible',\n", "            'inscription_par_metier': 'Inscriptions_Metier',\n", "            'presence_par_metier': 'Presence_Metier',\n", "            'appreciation_par_metier': 'Appreciation_Metier'\n", "        }, inplace=True)\n", "        return self.df\n", "\n", "cleaner = Cleaning(df_promotions)\n", "df_clean = cleaner.clean()\n", "df_clean.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonction eclatement"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame inscriptions créé avec 17 métiers\n", "\n", "Colonnes disponibles:\n", "['metier', 'total', 'eligible', 'noneligible', 'Promotion']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>metier</th>\n", "      <th>total</th>\n", "      <th>eligible</th>\n", "      <th>noneligible</th>\n", "      <th>Promotion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Maçon</td>\n", "      <td>219</td>\n", "      <td>83</td>\n", "      <td>136</td>\n", "      <td>PROMOTION 2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menuisier/Coffreur</td>\n", "      <td>71</td>\n", "      <td>30</td>\n", "      <td>41</td>\n", "      <td>PROMOTION 2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>361</td>\n", "      <td>119</td>\n", "      <td>242</td>\n", "      <td>PROMOTION 2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Carreleur</td>\n", "      <td>190</td>\n", "      <td>73</td>\n", "      <td>117</td>\n", "      <td>PROMOTION 2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Staffeur/Plaquiste</td>\n", "      <td>170</td>\n", "      <td>44</td>\n", "      <td>126</td>\n", "      <td>PROMOTION 2023</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               metier  total  eligible  noneligible       Promotion\n", "0               Maçon    219        83          136  PROMOTION 2023\n", "1  Menuisier/Coffreur     71        30           41  PROMOTION 2023\n", "2             Peintre    361       119          242  PROMOTION 2023\n", "3           Carreleur    190        73          117  PROMOTION 2023\n", "4  Staffeur/Plaquiste    170        44          126  PROMOTION 2023"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def explode_column(df, col_name, parent_col='Promotion'):\n", "    \"\"\"Éclate une colonne contenant des listes d'objets\"\"\"\n", "    rows = []\n", "    \n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        \n", "        # Vérifier que c'est une liste non vide\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    # Ajouter le nom de la promotion parent\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    \n", "    return pd.DataFrame(rows)\n", "\n", "# Éclatement des inscriptions par métier\n", "df_inscriptions = explode_column(df_clean, 'Inscriptions_Metier')\n", "print(f\"DataFrame inscriptions créé avec {len(df_inscriptions)} métiers\")\n", "print(\"\\nColonnes disponibles:\")\n", "print(df_inscriptions.columns.tolist())\n", "display(df_inscriptions.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def analyser_inscriptions_metier(promotion_nom, df_inscriptions):\n", "    \"\"\"Analyse et affiche les inscriptions par métier d'une promotion\"\"\"\n", "    print(f\"\\n PROMOTION: {promotion_nom}\")\n", "    \n", "    # Filtrer les inscriptions pour cette promotion\n", "    promo_inscriptions = df_inscriptions[df_inscriptions['Promotion'] == promotion_nom] if not df_inscriptions.empty else pd.DataFrame()\n", "    \n", "    # C<PERSON>er un DataFrame vide avec les colonnes attendues si pas de données\n", "    if promo_inscriptions.empty:\n", "        promo_inscriptions = pd.DataFrame(columns=['metier', 'total', 'eligible', 'noneligible'])\n", "    \n", "    print(f\" Nombre de métiers: {len(promo_inscriptions)}\")\n", "    print(\" Répartition par métier:\")\n", "    display(promo_inscriptions[['metier', 'total', 'eligible', 'noneligible']].reset_index(drop=True))\n", "    \n", "    if not promo_inscriptions.empty:\n", "        # Calcul des totaux\n", "        total_inscriptions = promo_inscriptions['total'].sum()\n", "        total_eligible = promo_inscriptions['eligible'].sum()\n", "        taux_eligibilite = (total_eligible / total_inscriptions * 100) if total_inscriptions > 0 else 0\n", "        \n", "        print(f\"\\n Résumé {promotion_nom}:\")\n", "        print(f\"  - Total inscriptions: {total_inscriptions:,}\")\n", "        print(f\"  - Total éligibles: {total_eligible:,}\")\n", "        print(f\"  - Taux d'éligibilité: {taux_eligibilite:.1f}%\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def analyser_appreciation_metier(promotion_nom, appreciation_data):\n", "    \"\"\"Analyse et affiche les appréciations par métier d'une promotion\"\"\"\n", "    print(f\"\\n PROMOTION: {promotion_nom}\")\n", "    \n", "    if isinstance(appreciation_data, dict) and appreciation_data:\n", "        # C<PERSON>er un DataFrame pour cette promotion\n", "        appreciation_rows = []\n", "        \n", "        for metier, appreciations in appreciation_data.items():\n", "            row = {\n", "                '<PERSON><PERSON><PERSON>': metier,\n", "                'Capable': appreciations.get('Capable', 0),\n", "                'Autonome': appreciations.get('Autonome', 0),\n", "                'Initier': appreciations.get('Initier', 0)\n", "            }\n", "            appreciation_rows.append(row)\n", "        \n", "        appreciation_df = pd.DataFrame(appreciation_rows)\n", "        print(\" Appréciations par métier:\")\n", "        display(appreciation_df)\n", "        \n", "        # Statistiques globales\n", "        total_capable = appreciation_df['Capable'].sum()\n", "        total_autonome = appreciation_df['Autonome'].sum()\n", "        total_initier = appreciation_df['Initier'].sum()\n", "        total_appreciations = total_capable + total_autonome + total_initier\n", "        \n", "        if total_appreciations > 0:\n", "            print(f\"\\n Répartition des appréciations:\")\n", "            print(f\"  - Capable: {total_capable} ({total_capable/total_appreciations*100:.1f}%)\")\n", "            print(f\"  - Autonome: {total_autonome} ({total_autonome/total_appreciations*100:.1f}%)\")\n", "            print(f\"  - À initier: {total_initier} ({total_initier/total_appreciations*100:.1f}%)\")\n", "    else:\n", "        # Afficher un DataFrame vide avec les en-têtes\n", "        appreciation_df_vide = pd.DataFrame(columns=['<PERSON><PERSON><PERSON>', 'Capable', 'Autonome', 'Initier'])\n", "        print(\" Appréciations par métier:\")\n", "        display(appreciation_df_vide)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def analyser_presence_metier(promotion_nom, presence_data):\n", "    \"\"\"Analyse et affiche les données de présence par métier d'une promotion\"\"\"\n", "    print(f\"\\n PROMOTION: {promotion_nom}\")\n", "    \n", "    if isinstance(presence_data, dict) and presence_data:\n", "        # C<PERSON>er un DataFrame pour cette promotion\n", "        presence_rows = []\n", "        \n", "        for metier, presences in presence_data.items():\n", "            row = {\n", "                '<PERSON><PERSON><PERSON>': metier,\n", "                'Travail': presences.get('travail', 0),\n", "                'Absence': presences.get('absence', 0),\n", "                'Retard': presences.get('retard', 0)\n", "            }\n", "            presence_rows.append(row)\n", "        \n", "        presence_df = pd.DataFrame(presence_rows)\n", "        print(\" Présences par métier:\")\n", "        display(presence_df)\n", "        \n", "        # Statistiques globales\n", "        total_travail = presence_df['Travail'].sum()\n", "        total_absence = presence_df['Absence'].sum()\n", "        total_retard = presence_df['Retard'].sum()\n", "        total_presences = total_travail + total_absence + total_retard\n", "        \n", "        if total_presences > 0:\n", "            print(f\"\\n Répartition des présences:\")\n", "            print(f\"  - Travail: {total_travail} ({total_travail/total_presences*100:.1f}%)\")\n", "            print(f\"  - Absence: {total_absence} ({total_absence/total_presences*100:.1f}%)\")\n", "            print(f\"  - Retard: {total_retard} ({total_retard/total_presences*100:.1f}%)\")\n", "        else:\n", "            print(\"  Aucune donnée de présence disponible\")\n", "    else:\n", "        # Afficher un DataFrame vide avec les en-têtes\n", "        presence_df_vide = pd.DataFrame(columns=['<PERSON><PERSON><PERSON>', 'Travail', 'Absence', 'Retard'])\n", "        print(\" Présences par métier:\")\n", "        display(presence_df_vide)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Inscriptions par Métier"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROMOTION: PROMOTION 2023\n", " Nombre de métiers: 7\n", " Répartition par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>metier</th>\n", "      <th>total</th>\n", "      <th>eligible</th>\n", "      <th>noneligible</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Maçon</td>\n", "      <td>219</td>\n", "      <td>83</td>\n", "      <td>136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menuisier/Coffreur</td>\n", "      <td>71</td>\n", "      <td>30</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>361</td>\n", "      <td>119</td>\n", "      <td>242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Carreleur</td>\n", "      <td>190</td>\n", "      <td>73</td>\n", "      <td>117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Staffeur/Plaquiste</td>\n", "      <td>170</td>\n", "      <td>44</td>\n", "      <td>126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Ferrailleur</td>\n", "      <td>92</td>\n", "      <td>37</td>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>107</td>\n", "      <td>46</td>\n", "      <td>61</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               metier  total  eligible  noneligible\n", "0               Maçon    219        83          136\n", "1  Menuisier/Coffreur     71        30           41\n", "2             <PERSON><PERSON><PERSON>    361       119          242\n", "3           Carreleur    190        73          117\n", "4  Staffeur/Plaquiste    170        44          126\n", "5         Ferrailleur     92        37           55\n", "6          <PERSON><PERSON><PERSON><PERSON>    107        46           61"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " Résumé PROMOTION 2023:\n", "  - Total inscriptions: 1,210\n", "  - Total éligibles: 432\n", "  - Taux d'éligibilité: 35.7%\n", "\n", " PROMOTION: PROMOTION 2024\n", " Nombre de métiers: 10\n", " Répartition par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>metier</th>\n", "      <th>total</th>\n", "      <th>eligible</th>\n", "      <th>noneligible</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAÇON</td>\n", "      <td>269</td>\n", "      <td>198</td>\n", "      <td>71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MENUISIER/COFFREUR</td>\n", "      <td>89</td>\n", "      <td>54</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PEINTRE</td>\n", "      <td>542</td>\n", "      <td>378</td>\n", "      <td>164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CARRELEUR</td>\n", "      <td>238</td>\n", "      <td>158</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>STAFFEUR/PLAQUISTE</td>\n", "      <td>142</td>\n", "      <td>93</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FERRAILLEUR</td>\n", "      <td>186</td>\n", "      <td>127</td>\n", "      <td>59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>FERRONNIER</td>\n", "      <td>179</td>\n", "      <td>106</td>\n", "      <td>73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ELECTRICIEN BATIMENT</td>\n", "      <td>1222</td>\n", "      <td>858</td>\n", "      <td>364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MONTEUR-RACCORDEUR FIBRE OPTIQUE</td>\n", "      <td>666</td>\n", "      <td>446</td>\n", "      <td>220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>PLOMBIER</td>\n", "      <td>213</td>\n", "      <td>139</td>\n", "      <td>74</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             metier  total  eligible  noneligible\n", "0                             MAÇON    269       198           71\n", "1                MENUISIER/COFFREUR     89        54           35\n", "2                           PEINTRE    542       378          164\n", "3                         CARRELEUR    238       158           80\n", "4                STAFFEUR/PLAQUISTE    142        93           49\n", "5                       FERRAILLEUR    186       127           59\n", "6                        FERRONNIER    179       106           73\n", "7              ELECTRICIEN BATIMENT   1222       858          364\n", "8  MONTEUR-RACCORDEUR FIBRE OPTIQUE    666       446          220\n", "9                          PLOMBIER    213       139           74"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " Résumé PROMOTION 2024:\n", "  - Total inscriptions: 3,746\n", "  - Total éligibles: 2,557\n", "  - Taux d'éligibilité: 68.3%\n"]}], "source": ["for _, promotion_row in df_clean.iterrows():\n", "    promotion_nom = promotion_row['Promotion']\n", "    analyser_inscriptions_metier(promotion_nom, df_inscriptions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Appréciations par Métier"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROMOTION: PROMOTION 2023\n", " Appréciations par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Capable</th>\n", "      <th>Autonome</th>\n", "      <th>Initier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Maçon</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menuisier/Coffreur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Carreleur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Staffeur/Plaquiste</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Ferrailleur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Métier  Capable  Autonome  Initier\n", "0               Maçon        0         0       49\n", "1  Menuisier/Coffreur        0         0       19\n", "2             Peintre        0         0       44\n", "3           Carreleur        0         0       30\n", "4  Staffeur/Plaquiste        0         0       31\n", "5         Ferrailleur        0         0       22\n", "6          Ferronnier        0         0       25"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " Répartition des appréciations:\n", "  - Capable: 0 (0.0%)\n", "  - Autonome: 0 (0.0%)\n", "  - À initier: 220 (100.0%)\n", "\n", " PROMOTION: PROMOTION 2024\n", " Appréciations par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Capable</th>\n", "      <th>Autonome</th>\n", "      <th>Initier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAÇON</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MENUISIER/COFFREUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PEINTRE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CARRELEUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>STAFFEUR/PLAQUISTE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FERRAILLEUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>FERRONNIER</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ELECTRICIEN BATIMENT</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MONTEUR-RACCORDEUR FIBRE OPTIQUE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>PLOMBIER</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>19</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             Métier  Capable  Autonome  Initier\n", "0                             MAÇON        0         0       56\n", "1                MENUISIER/COFFREUR        0         0       20\n", "2                           PEINTRE        0         0       32\n", "3                         CARRELEUR        0         0       28\n", "4                STAFFEUR/PLAQUISTE        0         0       16\n", "5                       FERRAILLEUR        0         0       41\n", "6                        FERRONNIER        0         0       16\n", "7              ELECTRICIEN BATIMENT        0         0       25\n", "8  MONTEUR-RACCORDEUR FIBRE OPTIQUE        0         0       13\n", "9                          PLOMBIER        0         0       19"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " Répartition des appréciations:\n", "  - Capable: 0 (0.0%)\n", "  - Autonome: 0 (0.0%)\n", "  - À initier: 266 (100.0%)\n"]}], "source": ["for _, promotion_row in df_clean.iterrows():\n", "    promotion_nom = promotion_row['Promotion']\n", "    appreciation_data = promotion_row['Appreciation_Metier']\n", "    analyser_appreciation_metier(promotion_nom, appreciation_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Présences par Métier"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROMOTION: PROMOTION 2023\n", " Présences par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Travail</th>\n", "      <th>Absence</th>\n", "      <th>Retard</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Maçon</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menuisier/Coffreur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Carreleur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Staffeur/Plaquiste</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Ferrailleur</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               M<PERSON>tier  Travail  Absence  Retard\n", "0               Maçon        0        0       0\n", "1  Menuisier/Coffreur        0        0       0\n", "2             Peintre        0        0       0\n", "3           Carreleur        0        0       0\n", "4  Staffeur/Plaquiste        0        0       0\n", "5         Ferrailleur        0        0       0\n", "6          Ferronnier        0        0       0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Au<PERSON>ne donn<PERSON> de présence disponible\n", "\n", " PROMOTION: PROMOTION 2024\n", " Présences par métier:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Travail</th>\n", "      <th>Absence</th>\n", "      <th>Retard</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAÇON</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MENUISIER/COFFREUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PEINTRE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CARRELEUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>STAFFEUR/PLAQUISTE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FERRAILLEUR</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>FERRONNIER</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ELECTRICIEN BATIMENT</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MONTEUR-RACCORDEUR FIBRE OPTIQUE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>PLOMBIER</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             M<PERSON>tier  Travail  Absence  Retard\n", "0                             MAÇON        0        0       0\n", "1                MENUISIER/COFFREUR        0        0       0\n", "2                           PEINTRE        0        0       0\n", "3                         CARRELEUR        0        0       0\n", "4                STAFFEUR/PLAQUISTE        0        0       0\n", "5                       FERRAILLEUR        0        0       0\n", "6                        FERRONNIER        0        0       0\n", "7              ELECTRICIEN BATIMENT        0        0       0\n", "8  MONTEUR-RACCORDEUR FIBRE OPTIQUE        0        0       0\n", "9                          PLOMBIER        0        0       0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Au<PERSON>ne donn<PERSON> de présence disponible\n"]}], "source": ["for _, promotion_row in df_clean.iterrows():\n", "    promotion_nom = promotion_row['Promotion']\n", "    presence_data = promotion_row['Presence_Metier']\n", "    analyser_presence_metier(promotion_nom, presence_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyse Comparative entre Promotions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(\"\\n ANALYSE COMPARATIVE ENTRE PROMOTIONS\")\n", "# print(\"=\"*50)\n", "\n", "# # <PERSON><PERSON><PERSON><PERSON>\n", "# print(\"\\n🎓 Résumé par promotion:\")\n", "# summary_data = []\n", "# for _, row in df_clean.iterrows():\n", "#     summary_data.append({\n", "#         'Promotion': row['Promotion'],\n", "#         'Total_General': row['Total_General'],\n", "#         'Total_Eligible': row['Total_Eligible'],\n", "#         'Total_NonEligible': row['Total_NonEligible'],\n", "#         'Taux_Eligibilite': round((row['Total_Eligible'] / row['Total_General'] * 100), 2) if row['Total_General'] > 0 else 0\n", "#     })\n", "\n", "# summary_df = pd.DataFrame(summary_data)\n", "# display(summary_df)\n", "\n", "# # Évolution entre les promotions\n", "# if len(summary_df) > 1:\n", "#     print(\"\\n📈 Évolution entre les promotions:\")\n", "#     evolution = summary_df.iloc[-1]['Total_General'] - summary_df.iloc[0]['Total_General']\n", "#     evolution_pct = (evolution / summary_df.iloc[0]['Total_General'] * 100) if summary_df.iloc[0]['Total_General'] > 0 else 0\n", "#     print(f\"  - Évolution des inscriptions: {evolution:+,} ({evolution_pct:+.1f}%)\")\n", "    \n", "#     evolution_eligible = summary_df.iloc[-1]['Total_Eligible'] - summary_df.iloc[0]['Total_Eligible']\n", "#     evolution_eligible_pct = (evolution_eligible / summary_df.iloc[0]['Total_Eligible'] * 100) if summary_df.iloc[0]['Total_Eligible'] > 0 else 0\n", "#     print(f\"  - Évolution des éligibles: {evolution_eligible:+,} ({evolution_eligible_pct:+.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Top Métiers par Promotion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if not df_inscriptions.empty:\n", "#     print(\"\\n🏆 TOP MÉTIERS PAR PROMOTION\")\n", "#     print(\"=\"*40)\n", "    \n", "#     for promotion in df_inscriptions['Promotion'].unique():\n", "#         promo_data = df_inscriptions[df_inscriptions['Promotion'] == promotion]\n", "#         top_metiers = promo_data.nlargest(3, 'total')\n", "        \n", "#         print(f\"\\n📋 {promotion}:\")\n", "#         for _, metier in top_metiers.iterrows():\n", "#             taux = (metier['eligible'] / metier['total'] * 100) if metier['total'] > 0 else 0\n", "#             print(f\"  {metier['metier']}: {metier['total']} inscrits ({taux:.1f}% éligibles)\")\n", "    \n", "#     # Métiers les plus populaires globalement\n", "#     print(\"\\n🌟 MÉTIERS LES PLUS POPULAIRES (toutes promotions)\")\n", "#     print(\"=\"*50)\n", "#     metiers_globaux = df_inscriptions.groupby('metier')['total'].sum().sort_values(ascending=False).head(5)\n", "#     for metier, total in metiers_globaux.items():\n", "#         print(f\"  {metier}: {total} inscriptions totales\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}