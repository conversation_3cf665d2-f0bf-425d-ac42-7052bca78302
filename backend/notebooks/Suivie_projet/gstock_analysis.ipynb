{"cells": [{"cell_type": "markdown", "id": "6978d44d", "metadata": {}, "source": ["# ANALYSE GLOBALE DE LA GESTION DE STOCK\n", "## Description\n", "Ce notebook fournit une analyse complète et intégrée de toutes les données de gestion de stock :\n", "- Approvisionnements\n", "- <PERSON><PERSON><PERSON>\n", "- Consommation\n", "- Sorties\n", "\n", "L'objectif est d'avoir une vue d'ensemble de la gestion de stock et des relations entre les différentes opérations."]}, {"cell_type": "code", "execution_count": null, "id": "e4ecd3b9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import json\n", "import ast\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go"]}, {"cell_type": "markdown", "id": "cdd662d8", "metadata": {}, "source": ["## Chargement des données\n", "Chargement de tous les fichiers CSV liés à la gestion de stock"]}, {"cell_type": "code", "execution_count": null, "id": "4d0ac5d9", "metadata": {}, "outputs": [], "source": ["class StockDataLoader:\n", "    def __init__(self):\n", "        self.files = {\n", "            # Fichiers CSV générés par les notebooks individuels\n", "            'achat': 'gstockachat_data.csv',\n", "            'consommation': 'gstockconsommation_data.csv', \n", "            'sortie': 'gstocksortie_data.csv',\n", "            # Fichiers CSV d'approvisionnement (multiples)\n", "            'programmes': 'gstock_programmes.csv',\n", "            'approvisionnements': 'gstock_approvisionnements.csv',\n", "            'items': 'gstock_items.csv',\n", "            'produits': 'gstock_produits.csv'\n", "        }\n", "        self.dataframes = {}\n", "        \n", "    def load_csv(self, name, file_path):\n", "        \"\"\"Charger un fichier CSV avec gestion d'erreurs\"\"\"\n", "        try:\n", "            df = pd.read_csv(file_path)\n", "            print(f\"✓ {name}: <PERSON><PERSON><PERSON> avec succ<PERSON> ({df.shape[0]} lignes, {df.shape[1]} colonnes)\")\n", "            return df\n", "        except FileNotFoundError:\n", "            print(f\"✗ {name}: <PERSON><PERSON><PERSON> {file_path} non trouvé\")\n", "            return pd.DataFrame()\n", "        except Exception as e:\n", "            print(f\"✗ {name}: Erreur lors du chargement - {str(e)}\")\n", "            return pd.DataFrame()\n", "    \n", "    def load_all(self):\n", "        \"\"\"Charger tous les fichiers CSV\"\"\"\n", "        print(\"📥 CHARGEMENT DES DONNÉES G-STOCK\")\n", "        print(\"=\" * 40)\n", "        for name, file_path in self.files.items():\n", "            self.dataframes[name] = self.load_csv(name, file_path)\n", "            \n", "    def get_data(self, name):\n", "        \"\"\"Récupérer un DataFrame spécifique\"\"\"\n", "        return self.dataframes.get(name, pd.DataFrame())\n", "    \n", "    def get_summary(self):\n", "        \"\"\"Obtenir un résumé de tous les DataFrames chargés\"\"\"\n", "        print(\"\\n📊 <PERSON><PERSON><PERSON><PERSON>É DES DONNÉES CHARGÉES:\")\n", "        for name, df in self.dataframes.items():\n", "            if not df.empty:\n", "                print(f\"  {name}: {df.shape[0]:,} lignes, {df.shape[1]} colonnes\")\n", "            else:\n", "                print(f\"  {name}: <PERSON><PERSON><PERSON> donn<PERSON>\")\n", "\n", "# Charger toutes les données\n", "loader = StockDataLoader()\n", "loader.load_all()\n", "loader.get_summary()\n", "\n", "# Récupérer les DataFrames\n", "df_achat = loader.get_data('achat')\n", "df_conso = loader.get_data('consommation')\n", "df_sortie = loader.get_data('sortie')\n", "df_programmes = loader.get_data('programmes')\n", "df_approvisionnements = loader.get_data('approvisionnements')\n", "df_items = loader.get_data('items')\n", "df_produits = loader.get_data('produits')"]}, {"cell_type": "markdown", "id": "7e548483", "metadata": {}, "source": ["## Analyse Descriptive des Stocks\n", "\n", "Dans cette section, nous allons analyser les différentes composantes du stock:\n", "\n", "1. Vue d'ensemble des approvisionnements\n", "2. <PERSON><PERSON><PERSON> a<PERSON>\n", "3. Suivi des consommations\n", "4. Gestion des sorties\n", "5. Indicateurs de performance clés (KPIs)"]}, {"cell_type": "code", "execution_count": null, "id": "7a6e850b", "metadata": {}, "outputs": [], "source": ["def analyze_integrated_gstock_data():\n", "    \"\"\"Analyse intég<PERSON>e de tous les modules G-Stock\"\"\"\n", "    \n", "    print(\"📊 ANALYSE INTÉGRÉE G-STOCK\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 1. Vue d'ensemble des modules\n", "    print(\"\\n🔍 VUE D'ENSEMBLE DES MODULES:\")\n", "    modules_summary = {\n", "        'Programmes': len(df_programmes) if not df_programmes.empty else 0,\n", "        'Approvisionnements': len(df_approvisionnements) if not df_approvisionnements.empty else 0,\n", "        'Items': len(df_items) if not df_items.empty else 0,\n", "        'Produits uniques': df_produits['produit_id'].nunique() if not df_produits.empty else 0,\n", "        'Achats': len(df_achat) if not df_achat.empty else 0,\n", "        'Consommations': len(df_conso) if not df_conso.empty else 0,\n", "        'Sorties': len(df_sortie) if not df_sortie.empty else 0\n", "    }\n", "    \n", "    for module, count in modules_summary.items():\n", "        print(f\"  {module}: {count:,}\")\n", "    \n", "    # 2. Analyse des flux de quantités\n", "    print(\"\\n📦 ANALYSE DES FLUX DE QUANTITÉS:\")\n", "    \n", "    # Quantités par module\n", "    qty_summary = {}\n", "    \n", "    if not df_items.empty and 'quantite' in df_items.columns:\n", "        qty_summary['Approvisionnement'] = df_items['quantite'].sum()\n", "    \n", "    if not df_achat.empty and 'quantity' in df_achat.columns:\n", "        qty_summary['Achat'] = df_achat['quantity'].sum()\n", "    \n", "    if not df_conso.empty and 'quantity_consumed' in df_conso.columns:\n", "        qty_summary['Consommation'] = df_conso['quantity_consumed'].sum()\n", "    \n", "    if not df_sortie.empty and 'quantity_out' in df_sortie.columns:\n", "        qty_summary['Sortie'] = df_sortie['quantity_out'].sum()\n", "    \n", "    for flux, qty in qty_summary.items():\n", "        print(f\"  {flux}: {qty:,.0f} unités\")\n", "    \n", "    # 3. <PERSON><PERSON><PERSON>û<PERSON> (si disponible)\n", "    if not df_achat.empty and 'total_cost' in df_achat.columns:\n", "        print(\"\\n💰 ANALYSE DES COÛTS:\")\n", "        total_cost = df_achat['total_cost'].sum()\n", "        avg_cost = df_achat['total_cost'].mean()\n", "        print(f\"  Coût total des achats: {total_cost:,.0f} FCFA\")\n", "        print(f\"  Coût moyen par achat: {avg_cost:,.0f} FCFA\")\n", "    \n", "    # 4. Top produits transversaux\n", "    print(\"\\n🏆 TOP PRODUITS TRANSVERSAUX:\")\n", "    \n", "    # Combiner les données de produits de différents modules\n", "    all_products = []\n", "    \n", "    if not df_produits.empty:\n", "        products_appro = df_produits.groupby('produit_libelle')['quantite_item'].sum()\n", "        for product, qty in products_appro.items():\n", "            all_products.append({'produit': product, 'source': 'Approvisionnement', 'quantite': qty})\n", "    \n", "    if not df_achat.empty and 'item_name' in df_achat.columns:\n", "        products_achat = df_achat.groupby('item_name')['quantity'].sum()\n", "        for product, qty in products_achat.items():\n", "            all_products.append({'produit': product, 'source': 'Achat', 'quantite': qty})\n", "    \n", "    if all_products:\n", "        df_all_products = pd.DataFrame(all_products)\n", "        top_products = df_all_products.groupby('produit')['quantite'].sum().nlargest(5)\n", "        \n", "        for i, (product, qty) in enumerate(top_products.items(), 1):\n", "            print(f\"  {i}. {product}: {qty:,.0f} unités\")\n", "    \n", "    return modules_summary, qty_summary\n", "\n", "# Effectuer l'analyse intégrée\n", "modules_summary, qty_summary = analyze_integrated_gstock_data()"]}, {"cell_type": "code", "execution_count": null, "id": "2be837f5", "metadata": {}, "outputs": [], "source": ["def analyze_achats(df):\n", "    \"\"\"Analyse des achats\"\"\"\n", "    if df is None:\n", "        print(\"Pas de données d'achat disponibles\")\n", "        return\n", "    \n", "    print(\"=== Statistiques des Achats ===\")\n", "    print(f\"Nombre total d'achats: {df.shape[0]}\")\n", "    \n", "    # Ana<PERSON><PERSON> temporelle\n", "    df['date'] = pd.to_datetime(df['date'])\n", "    monthly_achats = df.groupby(df['date'].dt.strftime('%Y-%m')).agg({\n", "        'quantite': ['count', 'sum'],\n", "        'prix_total': 'sum'\n", "    })\n", "    \n", "    # Visualisation\n", "    fig = make_subplots(rows=2, cols=1,\n", "                        subplot_titles=('Quantités achetées par mois',\n", "                                      'Montant total des achats par mois'))\n", "    \n", "    fig.add_trace(go.Bar(x=monthly_achats.index, \n", "                        y=monthly_achats[('quantite', 'sum')],\n", "                        name='Quantité achetée'),\n", "                  row=1, col=1)\n", "    \n", "    fig.add_trace(go.Bar(x=monthly_achats.index,\n", "                        y=monthly_achats[('prix_total', 'sum')],\n", "                        name='Montant total'),\n", "                  row=2, col=1)\n", "    \n", "    fig.update_layout(height=800, showlegend=True,\n", "                     title_text=\"<PERSON><PERSON><PERSON> des Achats\")\n", "    fig.show()\n", "\n", "# Analyser les achats\n", "analyze_achats(df_achat)"]}, {"cell_type": "code", "execution_count": null, "id": "ef9f4516", "metadata": {}, "outputs": [], "source": ["def analyze_consommations(df):\n", "    \"\"\"Analyse des consommations\"\"\"\n", "    if df is None:\n", "        print(\"Pas de données de consommation disponibles\")\n", "        return\n", "    \n", "    print(\"=== Statistiques des Consommations ===\")\n", "    print(f\"Nombre total de consommations: {df.shape[0]}\")\n", "    \n", "    # Ana<PERSON><PERSON> temporelle\n", "    df['date'] = pd.to_datetime(df['date'])\n", "    monthly_conso = df.groupby(df['date'].dt.strftime('%Y-%m')).agg({\n", "        'quantite': ['count', 'sum']\n", "    })\n", "    \n", "    # Top articles consommés\n", "    top_articles = df.groupby('article')['quantite'].sum().sort_values(ascending=False).head(10)\n", "    \n", "    # Visualisation\n", "    fig = make_subplots(rows=2, cols=1,\n", "                        subplot_titles=('Consommation mensuelle',\n", "                                      'Top 10 des articles les plus consommés'))\n", "    \n", "    fig.add_trace(go.Bar(x=monthly_conso.index,\n", "                        y=monthly_conso[('quantite', 'sum')],\n", "                        name='Quantité consommée'),\n", "                  row=1, col=1)\n", "    \n", "    fig.add_trace(go.Bar(x=top_articles.index,\n", "                        y=top_articles.values,\n", "                        name='Top articles'),\n", "                  row=2, col=1)\n", "    \n", "    fig.update_layout(height=800, showlegend=True,\n", "                     title_text=\"Analy<PERSON> des Consommations\")\n", "    fig.show()\n", "\n", "# Analyser les consommations\n", "analyze_consommations(df_conso)"]}, {"cell_type": "code", "execution_count": null, "id": "9d11f4a6", "metadata": {}, "outputs": [], "source": ["def analyze_sorties(df):\n", "    \"\"\"Analyse des sorties de stock\"\"\"\n", "    if df is None:\n", "        print(\"Pas de données de sortie disponibles\")\n", "        return\n", "    \n", "    print(\"=== Statistiques des Sorties ===\")\n", "    print(f\"Nombre total de sorties: {df.shape[0]}\")\n", "    \n", "    # Ana<PERSON><PERSON> temporelle\n", "    df['date'] = pd.to_datetime(df['date'])\n", "    monthly_sorties = df.groupby(df['date'].dt.strftime('%Y-%m')).agg({\n", "        'quantite': ['count', 'sum']\n", "    })\n", "    \n", "    # Analyse par motif de sortie\n", "    sorties_par_motif = df.groupby('motif')['quantite'].agg(['count', 'sum'])\n", "    \n", "    # Visualisation\n", "    fig = make_subplots(rows=2, cols=1,\n", "                        subplot_titles=('Évolution des sorties mensuelles',\n", "                                      'Répartition des sorties par motif'))\n", "    \n", "    fig.add_trace(go.Bar(x=monthly_sorties.index,\n", "                        y=monthly_sorties[('quantite', 'sum')],\n", "                        name='Quantité sortie'),\n", "                  row=1, col=1)\n", "    \n", "    fig.add_trace(go.Pie(labels=sorties_par_motif.index,\n", "                        values=sorties_par_motif['sum'],\n", "                        name='<PERSON><PERSON><PERSON> de sortie'),\n", "                  row=2, col=1)\n", "    \n", "    fig.update_layout(height=800, showlegend=True,\n", "                     title_text=\"Analyse des Sorties de Stock\")\n", "    fig.show()\n", "\n", "# Analyser les sorties\n", "analyze_sorties(df_sortie)"]}, {"cell_type": "markdown", "id": "1442b89d", "metadata": {}, "source": ["## Indicateurs de Performance (KPIs)\n", "\n", "Cette section présente les indicateurs clés de performance pour la gestion des stocks:\n", "\n", "1. Taux de rotation des stocks\n", "2. <PERSON><PERSON> de rupture de stock\n", "3. <PERSON><PERSON><PERSON> moyen des stocks\n", "4. Efficacité des approvisionnements"]}, {"cell_type": "code", "execution_count": null, "id": "50c3a740", "metadata": {}, "outputs": [], "source": ["def calculate_kpis(df_appro, df_achat, df_conso, df_sortie):\n", "    \"\"\"Calcul des indicateurs de performance\"\"\"\n", "    \n", "    def safe_division(x, y):\n", "        return x / y if y != 0 else 0\n", "    \n", "    print(\"=== Indicateurs de Performance ===\\n\")\n", "    \n", "    # 1. Taux de rotation des stocks\n", "    if df_sortie is not None and df_appro is not None:\n", "        total_sorties = df_sortie['quantite'].sum()\n", "        stock_moyen = df_appro['quantite'].mean()\n", "        taux_rotation = safe_division(total_sorties, stock_moyen)\n", "        print(f\"Taux de rotation des stocks: {taux_rotation:.2f}\")\n", "    \n", "    # 2. <PERSON><PERSON> de rupture (estimation basée sur les demandes non satisfaites)\n", "    if df_sortie is not None:\n", "        demandes_total = len(df_sortie)\n", "        demandes_non_satisfaites = df_sortie[df_sortie['quantite'] == 0].shape[0]\n", "        taux_rupture = safe_division(demandes_non_satisfaites, demandes_total) * 100\n", "        print(f\"Taux de rupture de stock: {taux_rupture:.2f}%\")\n", "    \n", "    # 3. <PERSON><PERSON>t moyen des stocks\n", "    if df_achat is not None:\n", "        cout_moyen = df_achat['prix_total'].mean()\n", "        print(f\"Coût moyen par achat: {cout_moyen:.2f} FCFA\")\n", "    \n", "    # 4. Efficacité des approvisionnements\n", "    if df_appro is not None and df_conso is not None:\n", "        total_appro = df_appro['quantite'].sum()\n", "        total_conso = df_conso['quantite'].sum()\n", "        ratio_efficacite = safe_division(total_conso, total_appro) * 100\n", "        print(f\"Efficacité des approvisionnements: {ratio_efficacite:.2f}%\")\n", "    \n", "    # Visualisation des KPIs\n", "    kpis = {\n", "        'Taux de rotation': taux_rotation,\n", "        'Taux de rupture (%)': taux_rupture,\n", "        '<PERSON><PERSON><PERSON> moyen (FCFA)': cout_moyen,\n", "        'Efficacité (%)': ratio_efficacite\n", "    }\n", "    \n", "    fig = go.Figure(data=[\n", "        go.Bar(x=list(kpis.keys()), \n", "               y=list(kpis.values()),\n", "               text=[f'{val:.2f}' for val in kpis.values()],\n", "               textposition='auto')\n", "    ])\n", "    \n", "    fig.update_layout(title_text=\"Synthèse des KPIs\",\n", "                     yaxis_title=\"Valeur\",\n", "                     showlegend=False)\n", "    fig.show()\n", "\n", "def create_integrated_visualizations(modules_summary, qty_summary):\n", "    \"\"\"Crée des visualisations intégrées pour tous les modules G-Stock\"\"\"\n", "    \n", "    print(\"\\n📊 VISUALISATIONS INTÉGRÉES G-STOCK\")\n", "    print(\"=\" * 45)\n", "    \n", "    # 1. Graphique en barres - Vue d'ensemble des modules\n", "    if modules_summary:\n", "        fig1 = px.bar(\n", "            x=list(modules_summary.keys()),\n", "            y=list(modules_summary.values()),\n", "            title=\"📊 Vue d'Ensemble des Modules G-Stock\",\n", "            labels={'x': '<PERSON><PERSON><PERSON>', 'y': 'Nombre d\\'Enregistrements'},\n", "            color=list(modules_summary.values()),\n", "            color_continuous_scale='Viridis'\n", "        )\n", "        \n", "        fig1.update_layout(height=500, showlegend=False, title_x=0.5)\n", "        fig1.show()\n", "    \n", "    # 2. Graphique en secteurs - Répartition des flux de quantités\n", "    if qty_summary:\n", "        fig2 = px.pie(\n", "            values=list(qty_summary.values()),\n", "            names=list(qty_summary.keys()),\n", "            title=\"🔄 Répartition des Flux de Quantités\"\n", "        )\n", "        \n", "        fig2.update_traces(textposition='inside', textinfo='percent+label')\n", "        fig2.update_layout(title_x=0.5)\n", "        fig2.show()\n", "    \n", "    # 3. Graphique de flux - Cycle de vie des matériaux\n", "    if qty_summary:\n", "        # C<PERSON>er un diagramme de flux simplifié\n", "        flux_data = {\n", "            'Étape': ['Approvisionnement', 'Achat', 'Consommation', 'Sortie'],\n", "            'Quantité': [qty_summary.get(k, 0) for k in ['Approvisionnement', 'Achat', 'Consommation', 'Sortie']]\n", "        }\n", "        \n", "        fig3 = px.line(\n", "            flux_data,\n", "            x='Étape',\n", "            y='Quantité',\n", "            title=\"📈 Cycle de Vie des Matériaux G-Stock\",\n", "            markers=True,\n", "            line_shape='spline'\n", "        )\n", "        \n", "        fig3.update_layout(height=400, title_x=0.5)\n", "        fig3.show()\n", "\n", "# C<PERSON>er les visualisations intégrées\n", "create_integrated_visualizations(modules_summary, qty_summary)\n", "\n", "def calculate_integrated_kpis():\n", "    \"\"\"Calcule les KPIs intégrés pour tous les modules G-Stock\"\"\"\n", "    \n", "    print(\"\\n📊 INDICATEURS DE PERFORMANCE INTÉGRÉS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 1. Efficacité globale du système\n", "    if qty_summary.get('Approvisionnement', 0) > 0 and qty_summary.get('Sortie', 0) > 0:\n", "        efficacite_globale = (qty_summary['Sortie'] / qty_summary['Approvisionnement']) * 100\n", "        print(f\"🎯 Efficacité globale du système: {efficacite_globale:.1f}%\")\n", "    \n", "    # 2. Taux d'utilisation des stocks\n", "    if qty_summary.get('Consommation', 0) > 0 and qty_summary.get('Approvisionnement', 0) > 0:\n", "        taux_utilisation = (qty_summary['Consommation'] / qty_summary['Approvisionnement']) * 100\n", "        print(f\"📊 Taux d'utilisation des stocks: {taux_utilisation:.1f}%\")\n", "    \n", "    # 3. <PERSON><PERSON>/Consommation\n", "    if qty_summary.get('Sortie', 0) > 0 and qty_summary.get('Consommation', 0) > 0:\n", "        ratio_sortie_conso = qty_summary['Sortie'] / qty_summary['Consommation']\n", "        print(f\"🔄 Ratio Sortie/Consommation: {ratio_sortie_conso:.2f}\")\n", "    \n", "    # 4. Diversité des produits\n", "    if not df_produits.empty:\n", "        nb_produits_uniques = df_produits['produit_id'].nunique()\n", "        print(f\"🛠️ Nombre de produits uniques gérés: {nb_produits_uniques:,}\")\n", "    \n", "    # 5. Performance par programme (si données disponibles)\n", "    if not df_programmes.empty and not df_items.empty:\n", "        prog_performance = df_items.groupby('programme_nom')['quantite'].sum().nlargest(3)\n", "        print(f\"\\n🏆 TOP 3 PROGRAMMES PAR VOLUME:\")\n", "        for i, (prog, qty) in enumerate(prog_performance.items(), 1):\n", "            print(f\"  {i}. {prog}: {qty:,.0f} unités\")\n", "\n", "# Calculer les KPIs intégrés\n", "calculate_integrated_kpis()"]}, {"cell_type": "markdown", "id": "8ded212c", "metadata": {}, "source": ["## CONCLUSION ET R<PERSON>OMMANDATIONS INTÉGRÉES\n", "\n", "### 📋 Résumé de l'analyse intégrée G-Stock\n", "\n", "Cette analyse complète du système G-Stock nous a permis de :\n", "\n", "1. **📊 Intégrer 7 modules de données** :\n", "   - Programmes et Approvisionnements (structure complexe)\n", "   - Items et Produits (catalogue détaillé)\n", "   - Achats (transactions financières)\n", "   - Consommations (utilisation sur projets)\n", "   - Sorties (livraisons et distributions)\n", "\n", "2. **🔄 Analyser les flux complets** de matériaux du début à la fin\n", "3. **📈 Me<PERSON>r la performance** avec des KPIs intégrés\n", "4. **🔗 Identifier les relations** entre tous les modules\n", "5. **📊 Créer des visualisations** transversales\n", "\n", "### 🎯 Recommandations stratégiques\n", "\n", "**Optimisation opérationnelle :**\n", "- Harmoniser les processus entre approvisionnement et sortie\n", "- Standardiser les codes produits dans tous les modules\n", "- Améliorer la traçabilité des flux de matériaux\n", "\n", "**Gestion financière :**\n", "- <PERSON><PERSON><PERSON><PERSON> les coûts d'achat avec les consommations réelles\n", "- Optimiser les volumes d'approvisionnement par programme\n", "- Ré<PERSON><PERSON> les écarts entre prévisions et réalisations\n", "\n", "**Performance système :**\n", "- Surveiller l'efficacité globale (ratio sortie/approvisionnement)\n", "- Améliorer le taux d'utilisation des stocks\n", "- Ré<PERSON><PERSON> les délais entre approvisionnement et utilisation\n", "\n", "### 🔮 Perspectives d'évolution\n", "\n", "- **Prédiction des besoins** basée sur l'historique des consommations\n", "- **Optimisation automatique** des niveaux de stock\n", "- **<PERSON><PERSON><PERSON> intelligentes** pour les ruptures potentielles\n", "- **Tableaux de bord temps réel** pour le pilotage opérationnel\n", "\n", "Cette analyse constitue la base d'un système de gestion des stocks optimisé et data-driven pour l'ensemble des opérations G-Stock."]}, {"cell_type": "code", "execution_count": null, "id": "7d649a7f", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}