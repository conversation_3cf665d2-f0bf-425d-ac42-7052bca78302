{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE DES DONNÉES G-STOCK ACHAT\n", "## Liste des imports utiles"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: gstockachat_data.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Achat de ciment pour projet</td>\n", "      <td>{'item_name': 'Ciment Portland', 'quantity': 1...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id         title                  description  \\\n", "0   1  Achat Ciment  Achat de ciment pour projet   \n", "\n", "                                                data  \n", "0  {'item_name': 'Ciment Portland', 'quantity': 1...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = 'gstockachat_data.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")\n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parsing des données"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsing réussi\n", "\n", "Structure du champ 'data':\n", "{'item_name': 'Ciment Portland', 'quantity': 100, 'unit_price': 4500, 'total_cost': 450000, 'supplier': 'CIMAF', 'purchase_date': '2024-07-01'}\n"]}], "source": ["def parse_json_data(json_str):\n", "    \"\"\"Parse une chaîne JSON en dictionnaire Python\"\"\"\n", "    try:\n", "        parsed = ast.literal_eval(json_str)\n", "        print(\"Parsing réussi\")\n", "        return parsed\n", "    except:\n", "        print(\"Erreur de parsing, retour de la chaîne originale\")\n", "        return json_str\n", "\n", "# Test du parsing sur le premier enregistrement\n", "if not df_raw.empty and 'data' in df_raw.columns:\n", "    sample_data = parse_json_data(df_raw['data'].iloc[0])\n", "    print(\"\\nStructure du champ 'data':\")\n", "    print(sample_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Création du dataframe parent"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame parent créé avec 1 achats\n", "\n", "Aperçu du DataFrame parent:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Achat de ciment pour projet</td>\n", "      <td>{'item_name': 'Ciment Portland', 'quantity': 1...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id         title                  description  \\\n", "0   1  Achat Ciment  Achat de ciment pour projet   \n", "\n", "                                                data  \n", "0  {'item_name': 'Ciment Portland', 'quantity': 1...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def create_parent_dataframe(df_raw):\n", "    \"\"\"Crée le DataFrame parent des achats\"\"\"\n", "    df = df_raw.copy()\n", "    print(f\"DataFrame parent créé avec {len(df)} achats\")\n", "    return df\n", "\n", "# Création du DataFrame parent\n", "df_achats = create_parent_dataframe(df_raw)\n", "print(\"\\nAperçu du DataFrame parent:\")\n", "display(df_achats.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Nettoyage des données"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nettoyage des données terminé\n", "\n", "Aperçu des données nettoyées:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Achat De Ciment Pour Projet</td>\n", "      <td>{'item_name': 'Ciment Portland', 'quantity': 1...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id         title                  description  \\\n", "0   1  Achat Ciment  Achat De Ciment Pour Projet   \n", "\n", "                                                data  \n", "0  {'item_name': 'Ciment Portland', 'quantity': 1...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def clean_data(df):\n", "    \"\"\"Nettoie les données en appliquant la capitalisation\"\"\"\n", "    df_clean = df.copy()\n", "    \n", "    # Capitalisation des champs texte\n", "    text_columns = ['title', 'description']\n", "    \n", "    for col in text_columns:\n", "        if col in df_clean.columns:\n", "            df_clean[col] = df_clean[col].astype(str).str.title()\n", "    \n", "    print(\"Nettoyage des données terminé\")\n", "    return df_clean\n", "\n", "# Application du nettoyage\n", "df_achats_clean = clean_data(df_achats)\n", "print(\"\\nAperçu des données nettoyées:\")\n", "display(df_achats_clean.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Éclatement des données imbriquées"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Données éclatées: 1 enregistrements\n", "Colonnes après éclatement: ['achat_id', 'title', 'description', 'item_name', 'quantity', 'unit_price', 'total_cost', 'supplier', 'purchase_date']\n", "\n", "Aperçu des données éclatées:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>achat_id</th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>item_name</th>\n", "      <th>quantity</th>\n", "      <th>unit_price</th>\n", "      <th>total_cost</th>\n", "      <th>supplier</th>\n", "      <th>purchase_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Achat De Ciment Pour Projet</td>\n", "      <td>Ciment Portland</td>\n", "      <td>100</td>\n", "      <td>4500</td>\n", "      <td>450000</td>\n", "      <td>CIMAF</td>\n", "      <td>2024-07-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   achat_id         title                  description        item_name  \\\n", "0         1  Achat Ciment  Achat De Ciment Pour Projet  Ciment Portland   \n", "\n", "   quantity  unit_price  total_cost supplier purchase_date  \n", "0       100        4500      450000    CIMAF    2024-07-01  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def explode_data_field(df):\n", "    \"\"\"Éclate le champ 'data' qui contient les détails des achats\"\"\"\n", "    if 'data' not in df.columns:\n", "        print(\"Pas de champ 'data' à éclater\")\n", "        return df\n", "    \n", "    exploded_records = []\n", "    \n", "    for _, row in df.iterrows():\n", "        # Données de base\n", "        base_data = {\n", "            'achat_id': row.get('id'),\n", "            'title': row.get('title', ''),\n", "            'description': row.get('description', '')\n", "        }\n", "        \n", "        # Éclatement du champ data\n", "        if pd.notna(row['data']):\n", "            try:\n", "                # Parse le JSON du champ data\n", "                if isinstance(row['data'], str):\n", "                    data_details = ast.literal_eval(row['data'])\n", "                else:\n", "                    data_details = row['data']\n", "                \n", "                # Ajouter tous les champs du data\n", "                if isinstance(data_details, dict):\n", "                    base_data.update(data_details)\n", "                    \n", "            except Exception as e:\n", "                print(f\"Erreur parsing data pour ligne {row.get('id')}: {e}\")\n", "                base_data['data_raw'] = row['data']\n", "        \n", "        exploded_records.append(base_data)\n", "    \n", "    df_exploded = pd.DataFrame(exploded_records)\n", "    print(f\"Données éclatées: {len(df_exploded)} enregistrements\")\n", "    print(f\"Colonnes après éclatement: {list(df_exploded.columns)}\")\n", "    return df_exploded\n", "\n", "# Éclatement des données\n", "df_achats_exploded = explode_data_field(df_achats_clean)\n", "print(\"\\nAperçu des données éclatées:\")\n", "display(df_achats_exploded.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyses et statistiques"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANALYSES DES ACHATS ===\n", "Nombre total d'achats: 1\n", "Coût total: 450,000 FCFA\n", "Coût moyen par achat: 450,000 FCFA\n", "\n", "Top 5 fournisseurs:\n"]}, {"data": {"text/plain": ["supplier\n", "CIMAF    1\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Top 5 articles:\n"]}, {"data": {"text/plain": ["item_name\n", "Ciment Portland    1\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Quantité totale achetée: 100\n", "Quantité moyenne par achat: 100.00\n"]}], "source": ["def analyze_achats(df):\n", "    \"\"\"Effectue des analyses sur les données d'achat\"\"\"\n", "    print(\"=== ANALYSES DES ACHATS ===\")\n", "    print(f\"Nombre total d'achats: {len(df)}\")\n", "    \n", "    # Analyses selon les colonnes disponibles\n", "    if 'total_cost' in df.columns:\n", "        print(f\"Coût total: {df['total_cost'].sum():,.0f} FCFA\")\n", "        print(f\"Coût moyen par achat: {df['total_cost'].mean():,.0f} FCFA\")\n", "    \n", "    if 'supplier' in df.columns:\n", "        print(\"\\nTop 5 fournisseurs:\")\n", "        top_suppliers = df['supplier'].value_counts().head()\n", "        display(top_suppliers)\n", "    \n", "    if 'item_name' in df.columns:\n", "        print(\"\\nTop 5 articles:\")\n", "        top_items = df['item_name'].value_counts().head()\n", "        display(top_items)\n", "    \n", "    if 'quantity' in df.columns:\n", "        print(f\"\\nQuantité totale achetée: {df['quantity'].sum():,.0f}\")\n", "        print(f\"Quantité moyenne par achat: {df['quantity'].mean():.2f}\")\n", "\n", "# Analyses des données\n", "analyze_achats(df_achats_exploded)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage des DataFrames finaux"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAMES FINAUX ===\n", "\n", "1. DataFrame des achats (nettoyé et éclaté):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>achat_id</th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>item_name</th>\n", "      <th>quantity</th>\n", "      <th>unit_price</th>\n", "      <th>total_cost</th>\n", "      <th>supplier</th>\n", "      <th>purchase_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Achat De Ciment Pour Projet</td>\n", "      <td>Ciment Portland</td>\n", "      <td>100</td>\n", "      <td>4500</td>\n", "      <td>450000</td>\n", "      <td>CIMAF</td>\n", "      <td>2024-07-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   achat_id         title                  description        item_name  \\\n", "0         1  Achat Ciment  Achat De Ciment Pour Projet  Ciment Portland   \n", "\n", "   quantity  unit_price  total_cost supplier purchase_date  \n", "0       100        4500      450000    CIMAF    2024-07-01  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Nombre total d'enregistrements: 1\n", "Colonnes disponibles: ['achat_id', 'title', 'description', 'item_name', 'quantity', 'unit_price', 'total_cost', 'supplier', 'purchase_date']\n"]}], "source": ["print(\"=== DATAFRAMES FINAUX ===\")\n", "print(\"\\n1. DataFrame des achats (nettoyé et éclaté):\")\n", "display(df_achats_exploded)\n", "\n", "print(f\"\\nNombre total d'enregistrements: {len(df_achats_exploded)}\")\n", "print(f\"Colonnes disponibles: {list(df_achats_exploded.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}