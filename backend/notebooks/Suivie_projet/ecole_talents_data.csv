status,message,data
success,Données Ecole des Talents récupérées et sauvegardées,"{'id': 1, 'title': 'Ecole des Talents Data Import', 'description': 'Import automatique du 2025-07-24 09:31:15.561836', 'data': {'data': [{'id': 1, 'name': 'PROMOTION 2023', 'total_general': 1210, 'total_eligible': 432, 'total_noneligible': 778, 'inscription_par_metier': [{'metier': 'Maçon', 'total': 219, 'eligible': 83, 'noneligible': 136}, {'metier': 'Menuisier/Coffreur', 'total': 71, 'eligible': 30, 'noneligible': 41}, {'metier': 'Peintre', 'total': 361, 'eligible': 119, 'noneligible': 242}, {'metier': 'Carreleur', 'total': 190, 'eligible': 73, 'noneligible': 117}, {'metier': 'Staffeur/Plaquiste', 'total': 170, 'eligible': 44, 'noneligible': 126}, {'metier': 'Ferrailleur', 'total': 92, 'eligible': 37, 'noneligible': 55}, {'metier': 'Ferronnier', 'total': 107, 'eligible': 46, 'noneligible': 61}], 'presence_par_metier': {'Maçon': {'travail': 0, 'absence': 0, 'retard': 0}, 'Menuisier/Coffreur': {'travail': 0, 'absence': 0, 'retard': 0}, 'Peintre': {'travail': 0, 'absence': 0, 'retard': 0}, 'Carreleur': {'travail': 0, 'absence': 0, 'retard': 0}, 'Staffeur/Plaquiste': {'travail': 0, 'absence': 0, 'retard': 0}, 'Ferrailleur': {'travail': 0, 'absence': 0, 'retard': 0}, 'Ferronnier': {'travail': 0, 'absence': 0, 'retard': 0}}, 'presence_par_chantier': [], 'presence_par_genre': [], 'appreciation_par_metier': {'Maçon': {'Capable': 0, 'Autonome': 0, 'Initier': 49}, 'Menuisier/Coffreur': {'Capable': 0, 'Autonome': 0, 'Initier': 19}, 'Peintre': {'Capable': 0, 'Autonome': 0, 'Initier': 44}, 'Carreleur': {'Capable': 0, 'Autonome': 0, 'Initier': 30}, 'Staffeur/Plaquiste': {'Capable': 0, 'Autonome': 0, 'Initier': 31}, 'Ferrailleur': {'Capable': 0, 'Autonome': 0, 'Initier': 22}, 'Ferronnier': {'Capable': 0, 'Autonome': 0, 'Initier': 25}}}, {'id': 2, 'name': 'PROMOTION 2024', 'total_general': 3746, 'total_eligible': 2557, 'total_noneligible': 1189, 'inscription_par_metier': [{'metier': 'MAÇON', 'total': 269, 'eligible': 198, 'noneligible': 71}, {'metier': 'MENUISIER/COFFREUR', 'total': 89, 'eligible': 54, 'noneligible': 35}, {'metier': 'PEINTRE', 'total': 542, 'eligible': 378, 'noneligible': 164}, {'metier': 'CARRELEUR', 'total': 238, 'eligible': 158, 'noneligible': 80}, {'metier': 'STAFFEUR/PLAQUISTE', 'total': 142, 'eligible': 93, 'noneligible': 49}, {'metier': 'FERRAILLEUR', 'total': 186, 'eligible': 127, 'noneligible': 59}, {'metier': 'FERRONNIER', 'total': 179, 'eligible': 106, 'noneligible': 73}, {'metier': 'ELECTRICIEN BATIMENT', 'total': 1222, 'eligible': 858, 'noneligible': 364}, {'metier': 'MONTEUR-RACCORDEUR FIBRE OPTIQUE', 'total': 666, 'eligible': 446, 'noneligible': 220}, {'metier': 'PLOMBIER', 'total': 213, 'eligible': 139, 'noneligible': 74}], 'presence_par_metier': {'MAÇON': {'travail': 0, 'absence': 0, 'retard': 0}, 'MENUISIER/COFFREUR': {'travail': 0, 'absence': 0, 'retard': 0}, 'PEINTRE': {'travail': 0, 'absence': 0, 'retard': 0}, 'CARRELEUR': {'travail': 0, 'absence': 0, 'retard': 0}, 'STAFFEUR/PLAQUISTE': {'travail': 0, 'absence': 0, 'retard': 0}, 'FERRAILLEUR': {'travail': 0, 'absence': 0, 'retard': 0}, 'FERRONNIER': {'travail': 0, 'absence': 0, 'retard': 0}, 'ELECTRICIEN BATIMENT': {'travail': 0, 'absence': 0, 'retard': 0}, 'MONTEUR-RACCORDEUR FIBRE OPTIQUE': {'travail': 0, 'absence': 0, 'retard': 0}, 'PLOMBIER': {'travail': 0, 'absence': 0, 'retard': 0}}, 'presence_par_chantier': [], 'presence_par_genre': [], 'appreciation_par_metier': {'MAÇON': {'Capable': 0, 'Autonome': 0, 'Initier': 56}, 'MENUISIER/COFFREUR': {'Capable': 0, 'Autonome': 0, 'Initier': 20}, 'PEINTRE': {'Capable': 0, 'Autonome': 0, 'Initier': 32}, 'CARRELEUR': {'Capable': 0, 'Autonome': 0, 'Initier': 28}, 'STAFFEUR/PLAQUISTE': {'Capable': 0, 'Autonome': 0, 'Initier': 16}, 'FERRAILLEUR': {'Capable': 0, 'Autonome': 0, 'Initier': 41}, 'FERRONNIER': {'Capable': 0, 'Autonome': 0, 'Initier': 16}, 'ELECTRICIEN BATIMENT': {'Capable': 0, 'Autonome': 0, 'Initier': 25}, 'MONTEUR-RACCORDEUR FIBRE OPTIQUE': {'Capable': 0, 'Autonome': 0, 'Initier': 13}, 'PLOMBIER': {'Capable': 0, 'Autonome': 0, 'Initier': 19}}}], 'status': 'success'}, 'created_at': '2025-07-24T09:31:15.562272Z', 'updated_at': '2025-07-24T09:31:15.562295Z'}"
