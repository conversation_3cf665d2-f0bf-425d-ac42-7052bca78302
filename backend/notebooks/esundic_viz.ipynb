{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE DES DONNÉES E-SYNDIC\n", "## Liste des imports utiles"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chargement du fichier CSV\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: E_syndic.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'id': 3, 'libelle': 'CALLISTO ETOILE', 'lieu...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                data\n", "0  [{'id': 3, 'libelle': 'CALLISTO ETOILE', 'lieu..."]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "csv_file = 'E_syndic.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")    \n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parsing des données"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsing réussi\n"]}], "source": ["def parse_json_data(json_str):\n", "        parsed = ast.literal_eval(json_str)\n", "        print(\"Parsing réussi\")\n", "        return parsed\n", "    \n", "# Parsing des données\n", "parsed_data = parse_json_data(df_raw['data'].iloc[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## creation du dataframe parent"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame créé avec 3 syndicats\n", "\n", "Colonnes disponibles:\n", "['id', 'libelle', 'lieu', 'pays', 'ville', 'superficie', 'description', 'created_at', 'proprietaire', 'locataires', 'charges', 'incidents', 'evenements']\n", "\n", " Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>libelle</th>\n", "      <th>lieu</th>\n", "      <th>pays</th>\n", "      <th>ville</th>\n", "      <th>superficie</th>\n", "      <th>description</th>\n", "      <th>created_at</th>\n", "      <th>proprietaire</th>\n", "      <th>locataires</th>\n", "      <th>charges</th>\n", "      <th>incidents</th>\n", "      <th>evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>300</td>\n", "      <td>None</td>\n", "      <td>2024-07-01 10:57:55</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>15000</td>\n", "      <td>None</td>\n", "      <td>2024-03-05 17:05:49</td>\n", "      <td>[{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>50000</td>\n", "      <td>None</td>\n", "      <td>2020-11-04 11:08:21</td>\n", "      <td>[{'name': 'AKOU', 'prenoms': 'YAPOIDOU AUGUSTI...</td>\n", "      <td>[{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...</td>\n", "      <td>{'appel_fonds': {'total_charge': 47625000, 'to...</td>\n", "      <td>[{'id': 101, 'reference': '250422091253', 'aut...</td>\n", "      <td>[{'id': 10, 'titre': 'Hello world', 'descripti...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                 libelle  lieu           pays             ville  \\\n", "0   3         CALLISTO ETOILE  None  Côte d'Ivoire      Grand-Bassam   \n", "1   2  CALLISTO BNETD PHASE 1  None  Côte d'Ivoire      Grand-Bassam   \n", "2   1               SYMPHONIA  None  Côte d'Ivoire  Abidjan - Cocody   \n", "\n", "  superficie description           created_at  \\\n", "0        300        None  2024-07-01 10:57:55   \n", "1      15000        None  2024-03-05 17:05:49   \n", "2      50000        None  2020-11-04 11:08:21   \n", "\n", "                                        proprietaire  \\\n", "0                                                 []   \n", "1  [{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...   \n", "2  [{'name': 'AKO<PERSON>', 'prenoms': 'YAPOIDOU AUGUSTI...   \n", "\n", "                                          locataires  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...   \n", "\n", "                                             charges  \\\n", "0  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "1  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "2  {'appel_fonds': {'total_charge': 47625000, 'to...   \n", "\n", "                                           incidents  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'id': 101, 'reference': '250422091253', 'aut...   \n", "\n", "                                          evenements  \n", "0                                                 []  \n", "1                                                 []  \n", "2  [{'id': 10, 'titre': 'Hello world', 'descripti...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_syndics = pd.DataFrame(parsed_data)\n", "print(f\"DataFrame créé avec {len(df_syndics)} syndicats\")\n", "\n", "# Affichage des colonnes disponibles\n", "print(\"\\nColonnes disponibles:\")\n", "print(df_syndics.columns.tolist())\n", "\n", "# Affichage des données de base\n", "print(\"\\n Aperçu des données:\")\n", "display(df_syndics.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Pays</th>\n", "      <th>Ville</th>\n", "      <th>Superficie</th>\n", "      <th>Date de creation</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>locataires</th>\n", "      <th>Charges</th>\n", "      <th>Incidents</th>\n", "      <th>Evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>300</td>\n", "      <td>2024-07-01 10:57:55</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>15000</td>\n", "      <td>2024-03-05 17:05:49</td>\n", "      <td>[{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>50000</td>\n", "      <td>2020-11-04 11:08:21</td>\n", "      <td>[{'name': 'AKOU', 'prenoms': 'YAPOIDOU AUGUSTI...</td>\n", "      <td>[{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...</td>\n", "      <td>{'appel_fonds': {'total_charge': 47625000, 'to...</td>\n", "      <td>[{'id': 101, 'reference': '250422091253', 'aut...</td>\n", "      <td>[{'id': 10, 'titre': 'Hello world', 'descripti...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme           Pays             Ville Superficie  \\\n", "0         CALLISTO ETOILE  Côte d'Ivoire      Grand-Bassam        300   \n", "1  CALLISTO BNETD PHASE 1  Côte d'Ivoire      Grand-Bassam      15000   \n", "2               SYMPHONIA  Côte d'Ivoire  Abidjan - Cocody      50000   \n", "\n", "      Date de creation                                       Proprietaire  \\\n", "0  2024-07-01 10:57:55                                                 []   \n", "1  2024-03-05 17:05:49  [{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...   \n", "2  2020-11-04 11:08:21  [{'name': 'AKOU', 'prenoms': 'YAPOIDOU AUGUSTI...   \n", "\n", "                                          locataires  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...   \n", "\n", "                                             Charges  \\\n", "0  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "1  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "2  {'appel_fonds': {'total_charge': 47625000, 'to...   \n", "\n", "                                           Incidents  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'id': 101, 'reference': '250422091253', 'aut...   \n", "\n", "                                          Evenements  \n", "0                                                 []  \n", "1                                                 []  \n", "2  [{'id': 10, 'titre': 'Hello world', 'descripti...  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["class Cleaning:\n", "    def __init__(self, df):\n", "        self.df = df.copy()\n", "\n", "    def clean(self):\n", "        # Supprimer colonnes inutiles\n", "        self.df.drop(columns=['id', 'lieu', 'description'], inplace=True)\n", "        # Renommer colonnes\n", "        self.df.rename(columns={\n", "            'libelle': 'Programme',\n", "            'created_at': 'Date de creation',\n", "            'pays': 'Pays',\n", "            'ville' : 'Ville',\n", "            'superficie' : 'Superficie',\n", "            'proprietaire' : 'Pro<PERSON>rietaire',\n", "            'loataires' : 'Locataires',\n", "            'charges' : 'Charges',\n", "            'incidents' : 'Incidents',\n", "            'evenements' : 'Evenements'\n", "            \n", "        }, inplace=True)\n", "        return self.df\n", "cleaner = Cleaning(df_syndics)\n", "df_clean = cleaner.clean()\n", "df_clean.head()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonction eclatement"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["def explode_column(df, col_name, parent_col='Programme'):\n", "    \"\"\"Éclate une colonne contenant des listes d'objets\"\"\"\n", "    rows = []\n", "    \n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        \n", "        # Vérifier que c'est une liste non vide\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    \n", "    return pd.DataFrame(rows)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Création des DataFrames éclatés"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Création des DataFrames éclatés...\n", "Colonnes propriétaires: ['name', 'prenoms', 'email', 'contact', 'Programme']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "      <th>Programme</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BLEOUE</td>\n", "      <td>EHUI ROGER</td>\n", "      <td>rog<PERSON><PERSON><PERSON><PERSON>@hotmail.com</td>\n", "      <td>0707037922</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KONAN</td>\n", "      <td>DOMINIQUE KOUADIO</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 06 82 85</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ASSOUMOU</td>\n", "      <td>KOKO</td>\n", "      <td>il<PERSON><EMAIL></td>\n", "      <td>07 57 97 49 23</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>YAO</td>\n", "      <td>GAISIE DORGELES</td>\n", "      <td>dorge<PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>0757320032</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KOUAKOU</td>\n", "      <td>KOUAME BENOIT</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 58 76 75</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       name            prenoms                    email         contact  \\\n", "0    BLEOUE         EHUI ROGER  roger<PERSON><PERSON><EMAIL>      0707037922   \n", "1     KONAN  DOMINIQUE KOUADIO     <EMAIL>  07 07 06 82 85   \n", "2  ASSOUMOU               KOKO       <EMAIL>  07 57 97 49 23   \n", "3       YAO    GAISIE DORGELES    <EMAIL>      0757320032   \n", "4   KOUAKOU      KOUAME BENOIT    <EMAIL>  07 07 58 76 75   \n", "\n", "                Programme  \n", "0  CALLISTO BNETD PHASE 1  \n", "1  CALLISTO BNETD PHASE 1  \n", "2  CALLISTO BNETD PHASE 1  \n", "3  CALLISTO BNETD PHASE 1  \n", "4  CALLISTO BNETD PHASE 1  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame locataires créé avec 0 entrées\n", "Aucun locataire trouvé\n", "\n", "DataFrame incidents créé avec 61 entrées\n", "Colonnes incidents: ['id', 'reference', 'auteur', 'date', 'description', 'status', 'visibilite', 'priorite', 'secteur', 'Programme']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>reference</th>\n", "      <th>auteur</th>\n", "      <th>date</th>\n", "      <th>description</th>\n", "      <th>status</th>\n", "      <th>visibilite</th>\n", "      <th>priorite</th>\n", "      <th>secteur</th>\n", "      <th>Programme</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>101</td>\n", "      <td>250422091253</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>25-04-2022 09:12</td>\n", "      <td>Problème d’électricité dans toute la cité dû à...</td>\n", "      <td>Traité</td>\n", "      <td>Public</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>100</td>\n", "      <td>240422022613</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>24-04-2022 14:26</td>\n", "      <td>&lt;p&gt;Pas d’électricité dans une partie de la vil...</td>\n", "      <td>Traité</td>\n", "      <td>Privé</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>99</td>\n", "      <td>160422092638</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>16-04-2022 09:26</td>\n", "      <td>&lt;p&gt;Perturbations de la fourniture du courant C...</td>\n", "      <td>Traité</td>\n", "      <td>Public</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>87</td>\n", "      <td>270222092002</td>\n", "      <td>MOUSOUNDA ANDREA VIRGINIE</td>\n", "      <td>27-02-2022 09:20</td>\n", "      <td>Infiltration d eau en cas de pluies fortes</td>\n", "      <td>Traité</td>\n", "      <td>Privé</td>\n", "      <td>Haute</td>\n", "      <td>Non défini</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>86</td>\n", "      <td>270222075142</td>\n", "      <td>DIBI SERGE PACOME</td>\n", "      <td>27-02-2022 07:51</td>\n", "      <td><PERSON><PERSON><PERSON>,\\n\\nDes parties du bâtiment sont en ch...</td>\n", "      <td>Traité</td>\n", "      <td>Privé</td>\n", "      <td>Haute</td>\n", "      <td>BTP</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id     reference                     auteur              date  \\\n", "0  101  250422091253            INOOVIM INOOVIM  25-04-2022 09:12   \n", "1  100  240422022613            INOOVIM INOOVIM  24-04-2022 14:26   \n", "2   99  160422092638            INOOVIM INOOVIM  16-04-2022 09:26   \n", "3   87  270222092002  MOUSOUNDA ANDREA VIRGINIE  27-02-2022 09:20   \n", "4   86  270222075142          DIBI SERGE PACOME  27-02-2022 07:51   \n", "\n", "                                         description  status visibilite  \\\n", "0  Problème d’électricité dans toute la cité dû à...  Traité     Public   \n", "1  <p>Pas d’électricité dans une partie de la vil...  <PERSON><PERSON><PERSON>      Privé   \n", "2  <p>Perturbations de la fourniture du courant C...  Traité     Public   \n", "3         Infiltration d eau en cas de pluies fortes  Traité      Privé   \n", "4  Bonjour,\\n\\nDes parties du bâtiment sont en ch...  Traité      Privé   \n", "\n", "  priorite      secteur  Programme  \n", "0    Haute  Electricité  SYMPHONIA  \n", "1    Haute  Electricité  SYMPHONIA  \n", "2    Haute  Electricité  SYMPHONIA  \n", "3    Haute   Non défini  SYMPHONIA  \n", "4    Haute          BTP  SYMPHONIA  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame événements créé avec 7 entrées\n", "Colonnes événements: ['id', 'titre', 'description', 'auteur', 'status', 'date_debut', 'heure_debut', 'date_fin', 'heure_fin', 'bruyant', 'Programme']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>titre</th>\n", "      <th>description</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>date_debut</th>\n", "      <th>heure_debut</th>\n", "      <th>date_fin</th>\n", "      <th>heure_fin</th>\n", "      <th>bruyant</th>\n", "      <th>Programme</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10</td>\n", "      <td>Hello world</td>\n", "      <td>Hello</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>10/03/2022</td>\n", "      <td>11:24</td>\n", "      <td>10/03/2022</td>\n", "      <td>19:30</td>\n", "      <td><PERSON>ui</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9</td>\n", "      <td>Anniversaire de ma fille</td>\n", "      <td>Anniversaire de ma fille</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>14/03/2022</td>\n", "      <td>14:34</td>\n", "      <td>14/03/2022</td>\n", "      <td>12:34</td>\n", "      <td><PERSON>ui</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>Test</td>\n", "      <td>Test</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>07/03/2022</td>\n", "      <td>14:27</td>\n", "      <td>07/03/2022</td>\n", "      <td>14:27</td>\n", "      <td><PERSON>ui</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7</td>\n", "      <td>ANNIVERSAIRE</td>\n", "      <td>Anniversaire au Pool House le samedi 12 mars 2...</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>En cours</td>\n", "      <td>03/12/2022</td>\n", "      <td>17:00</td>\n", "      <td>03/12/2022</td>\n", "      <td>23:00</td>\n", "      <td><PERSON>ui</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>Anniversaire</td>\n", "      <td>Anniversaire d’un enfant de 12 ans</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En cours</td>\n", "      <td>08/14/2021</td>\n", "      <td>12:00</td>\n", "      <td>08/14/2021</td>\n", "      <td>17:00</td>\n", "      <td><PERSON>ui</td>\n", "      <td>SYMPHONIA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                     titre  \\\n", "0  10               Hello world   \n", "1   9  Anniversaire de ma fille   \n", "2   8                      Test   \n", "3   7              ANNIVERSAIRE   \n", "4   6              Anniversaire   \n", "\n", "                                         description               auteur  \\\n", "0                                              Hello  KAKPO EDOU ZACHARIE   \n", "1                           Anniversaire de ma fille  KAKPO EDOU ZACHARIE   \n", "2                                               Test  KAKPO EDOU ZACHARIE   \n", "3  Anniversaire au Pool House le samedi 12 mars 2...      INOOVIM INOOVIM   \n", "4                 Anniversaire d’un enfant de 12 ans  KAKPO EDOU ZACHARIE   \n", "\n", "       status  date_debut heure_debut    date_fin heure_fin bruyant  Programme  \n", "0  En attente  10/03/2022       11:24  10/03/2022     19:30     Oui  SYMPHONIA  \n", "1  En attente  14/03/2022       14:34  14/03/2022     12:34     <PERSON><PERSON>  SYMPHONIA  \n", "2  En attente  07/03/2022       14:27  07/03/2022     14:27     <PERSON><PERSON>  SYMPHONIA  \n", "3    En cours  03/12/2022       17:00  03/12/2022     23:00     Oui  SYMPHONIA  \n", "4    En cours  08/14/2021       12:00  08/14/2021     17:00     Oui  SYMPHONIA  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " Tous les DataFrames ont été créés avec succès !\n"]}], "source": ["print(\"Création des DataFrames éclatés...\")\n", "\n", "df_proprietaires = explode_column(df_clean, 'Proprietaire')\n", "if not df_proprietaires.empty:\n", "    print(\"Colonnes propriétaires:\", df_proprietaires.columns.tolist())\n", "    display(df_proprietaires.head())\n", "else:\n", "    print(\"Aucun propriétaire trouvé\")\n", "    df_proprietaires = pd.DataFrame(columns=['Programme', 'name', 'prenoms', 'email', 'contact'])\n", "\n", "# DataFrame des locataires\n", "df_locataires = explode_column(df_clean, 'Locataires')\n", "print(f\"\\nDataFrame locataires créé avec {len(df_locataires)} entrées\")\n", "if not df_locataires.empty:\n", "    print(\"Colonnes locataires:\", df_locataires.columns.tolist())\n", "    display(df_locataires.head())\n", "else:\n", "    print(\"Aucun locataire trouvé\")\n", "    df_locataires = pd.DataFrame(columns=['Programme', 'name', 'prenoms', 'email', 'contact'])\n", "\n", "# DataFrame des incidents\n", "df_incidents = explode_column(df_clean, 'Incidents')\n", "print(f\"\\nDataFrame incidents créé avec {len(df_incidents)} entrées\")\n", "if not df_incidents.empty:\n", "    print(\"Colonnes incidents:\", df_incidents.columns.tolist())\n", "    display(df_incidents.head())\n", "else:\n", "    print(\"Aucun incident trouvé\")\n", "    df_incidents = pd.DataFrame(columns=['Programme', 'titre', 'description', 'status', 'date_creation'])\n", "\n", "# DataFrame des événements\n", "df_evenements = explode_column(df_clean, 'Evenements')\n", "print(f\"\\nDataFrame événements créé avec {len(df_evenements)} entrées\")\n", "if not df_evenements.empty:\n", "    print(\"Colonnes événements:\", df_evenements.columns.tolist())\n", "    display(df_evenements.head())\n", "else:\n", "    print(\"Aucun événement trouvé\")\n", "    df_evenements = pd.DataFrame(columns=['Programme', 'titre', 'description', 'date_debut', 'date_fin', 'auteur'])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["def analyser_proprietaires(programme_nom, df_proprietaires):\n", "    \"\"\"Analyse et affiche les propriétaires d'un programme\"\"\"\n", "    print(f\"\\n PROGRAMME: {programme_nom}\")\n", "    \n", "    # Filtrer les propriétaires pour ce programme\n", "    prog_proprietaires = df_proprietaires[df_proprietaires['Programme'] == programme_nom] if not df_proprietaires.empty else pd.DataFrame()\n", "    \n", "    # C<PERSON>er un DataFrame vide avec les colonnes attendues si pas de données\n", "    if prog_proprietaires.empty:\n", "        prog_proprietaires = pd.DataFrame(columns=['name', 'prenoms', 'email', 'contact'])\n", "    \n", "    print(f\" Nombre de propriétaires: {len(prog_proprietaires)}\")\n", "    print(\" Liste des propriétaires:\")\n", "    display(prog_proprietaires[['name', 'prenoms', 'email', 'contact']].reset_index(drop=True))\n", "    \n", "\n", "def analyser_locataires(programme_nom, df_locataires):\n", "    \"\"\"Analyse et affiche les locataires d'un programme\"\"\"\n", "    print(f\"\\n PROGRAMME: {programme_nom}\")\n", "    \n", "    # Filtrer les locataires pour ce programme\n", "    prog_locataires = df_locataires[df_locataires['Programme'] == programme_nom] if not df_locataires.empty else pd.DataFrame()\n", "    \n", "    # C<PERSON>er un DataFrame vide avec les colonnes attendues si pas de données\n", "    if prog_locataires.empty:\n", "        prog_locataires = pd.DataFrame(columns=['name', 'prenoms', 'email', 'contact'])\n", "    \n", "    print(f\" Nombre de locataires: {len(prog_locataires)}\")\n", "    print(\" Liste des locataires:\")\n", "    display(prog_locataires[['name', 'prenoms', 'email', 'contact']].reset_index(drop=True))\n", "\n", "\n", "def analyser_charges(programme_nom, charges_data):\n", "    \"\"\"Analyse et affiche les charges d'un programme\"\"\"\n", "    print(f\"\\n PROGRAMME: {programme_nom}\")\n", "    \n", "    if isinstance(charges_data, dict) and charges_data:\n", "        # A<PERSON>s de fonds\n", "        appel_fonds = charges_data.get('appel_fonds', {})\n", "        imprevus = charges_data.get('imprevus', {})\n", "        \n", "        # Créer un DataFrame pour ce programme\n", "        charge_data = {\n", "            'Type de charge': ['Charges régulières', 'Charges payées', 'Charges impayées', \n", "                              'Charges exceptionnelles', 'Exceptionnelles payées', 'Exceptionnelles impayées'],\n", "            'Montant (XOF)': [\n", "                f\"{appel_fonds.get('total_charge', 0):,.0f}\",\n", "                f\"{appel_fonds.get('total_payer', 0):,.0f}\",\n", "                f\"{appel_fonds.get('total_impayes', 0):,.0f}\",\n", "                f\"{imprevus.get('total_charge_exceptionnel', 0):,.0f}\",\n", "                f\"{imprevus.get('total_charge_exceptionnel_payer', 0):,.0f}\",\n", "                f\"{imprevus.get('total_charge_exceptionnel_impayer', 0):,.0f}\"\n", "            ]\n", "        }\n", "        \n", "        charges_df = pd.DataFrame(charge_data)\n", "        print(\" Détail des charges:\")\n", "        display(charges_df)\n", "        \n", "        # Calcul du taux de paiement\n", "        #total_charge = appel_fonds.get('total_charge', 0)\n", "        #total_paye = appel_fonds.get('total_payer', 0)\n", "        #if total_charge > 0:\n", "            #taux_paiement = (total_paye / total_charge) * 100\n", "            #print(f\" Taux de paiement: {taux_paiement:.1f}%\")\n", "    else:\n", "        # Afficher un DataFrame vide avec les en-têtes\n", "        charge_data_vide = {\n", "            'Type de charge': ['Charges régulières', 'Charges payées', 'Charges impayées', \n", "                              'Charges exceptionnelles', 'Exceptionnelles payées', 'Exceptionnelles impayées'],\n", "            'Montant (XOF)': ['0', '0', '0', '0', '0', '0']\n", "        }\n", "        charges_df_vide = pd.DataFrame(charge_data_vide)\n", "        print(\" Détail des charges:\")\n", "        display(charges_df_vide)\n", "\n", "def analyser_incidents(programme_nom, df_incidents):\n", "    \"\"\"Analyse et affiche les incidents d'un programme\"\"\"\n", "    print(f\"\\n PROGRAMME: {programme_nom}\")\n", "    \n", "    # Filtrer les incidents pour ce programme\n", "    prog_incidents = df_incidents[df_incidents['Programme'] == programme_nom] if not df_incidents.empty else pd.DataFrame()\n", "    \n", "    # C<PERSON>er un DataFrame vide avec les colonnes attendues si pas de données\n", "    if prog_incidents.empty:\n", "        prog_incidents = pd.DataFrame(columns=['reference', 'date', 'auteur', 'status', 'priorite', 'secteur'])\n", "    \n", "    print(f\" Nombre d'incidents: {len(prog_incidents)}\")\n", "    print(\" Liste des incidents:\")\n", "    display(prog_incidents[['reference', 'date', 'auteur', 'status', 'priorite', 'secteur']].reset_index(drop=True))\n", "    \n", "    #if not prog_incidents.empty:\n", "        # Statistiques pour ce programme\n", "        #print(\"\\n Répartition par statut:\")\n", "        #status_count = prog_incidents['status'].value_counts().reset_index()\n", "        #status_count.columns = ['Statut', 'Nombre']\n", "        #display(status_count)\n", "        \n", "        #print(\"\\n⚡ Répartition par priorité:\")\n", "        #priorite_count = prog_incidents['priorite'].value_counts().reset_index()\n", "        #priorite_count.columns = ['Priorité', 'Nombre']\n", "        #display(priorite_count)\n", "    #else:\n", "        #print(\" Ce programme pourra accueillir des incidents dans le futur\")\n", "\n", "def analyser_evenements(programme_nom, df_evenements):\n", "    \"\"\"Analyse et affiche les événements d'un programme\"\"\"\n", "    print(f\"\\n PROGRAMME: {programme_nom}\")\n", "    \n", "    # Filtrer les événements pour ce programme\n", "    prog_evenements = df_evenements[df_evenements['Programme'] == programme_nom] if not df_evenements.empty else pd.DataFrame()\n", "    \n", "    # C<PERSON>er un DataFrame vide avec les colonnes attendues si pas de données\n", "    if prog_evenements.empty:\n", "        prog_evenements = pd.DataFrame(columns=['titre', 'auteur', 'status', 'date_debut', 'heure_debut', 'bruyant'])\n", "    \n", "    print(f\" Nombre d'événements: {len(prog_evenements)}\")\n", "    print(\" Liste des événements:\")\n", "    display(prog_evenements[['titre', 'auteur', 'status', 'date_debut', 'heure_debut', 'bruyant']].reset_index(drop=True))\n", "    \n", "    #if not prog_evenements.empty:\n", "        # Statistiques pour ce programme\n", "        #print(\"\\n Répartition par statut:\")\n", "        #status_events = prog_evenements['status'].value_counts().reset_index()\n", "        #status_events.columns = ['Statut', 'Nombre']\n", "        #display(status_events)\n", "        \n", "        #print(\"\\n Événements bruyants:\")\n", "        #bruyant_count = prog_evenements['bruyant'].value_counts().reset_index()\n", "        #bruyant_count.columns = ['<PERSON><PERSON>yant', 'Nombre']\n", "        #display(bruyant_count)\n", "    #else:\n", "        #print(\" Ce programme pourra accueillir des événements dans le futur\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Classe de Métriques E-Syndic"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["class ESyndicMetrics:\n", "    \"\"\"\n", "    Classe réutilisable pour calculer toutes les métriques E-Syndic\n", "    \"\"\"\n", "    \n", "    def __init__(self, df_clean, df_proprietaires, df_locataires, df_incidents, df_evenements):\n", "        self.df_clean = df_clean\n", "        self.df_proprietaires = df_proprietaires\n", "        self.df_locataires = df_locataires\n", "        self.df_incidents = df_incidents\n", "        self.df_evenements = df_evenements\n", "    \n", "    def get_total_proprietaires_locataires_par_programme(self):\n", "        \"\"\"\n", "        Retourne le nombre total de propriétaires et locataires par programme\n", "        \"\"\"\n", "        results = []\n", "        \n", "        for _, row in self.df_clean.iterrows():\n", "            programme = row['Programme']\n", "            \n", "            # Compter proprié<PERSON>s\n", "            nb_proprietaires = len(self.df_proprietaires[self.df_proprietaires['Programme'] == programme]) if not self.df_proprietaires.empty else 0\n", "            \n", "            # Compter locataires\n", "            nb_locataires = len(self.df_locataires[self.df_locataires['Programme'] == programme]) if not self.df_locataires.empty else 0\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Nb_Proprietaires': nb_proprietaires,\n", "                'Nb_Locataires': nb_locataires,\n", "                'Total_Personnes': nb_proprietaires + nb_locataires\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_biens_par_programme(self):\n", "        \"\"\"\n", "        Retourne le nombre de biens par programme (basé sur le nombre de propriétaires + locataires)\n", "        \"\"\"\n", "        results = []\n", "        \n", "        for _, row in self.df_clean.iterrows():\n", "            programme = row['Programme']\n", "            \n", "            # E<PERSON><PERSON><PERSON> le nombre de biens (propriétaires + locataires uniques)\n", "            nb_proprietaires = len(self.df_proprietaires[self.df_proprietaires['Programme'] == programme]) if not self.df_proprietaires.empty else 0\n", "            nb_locataires = len(self.df_locataires[self.df_locataires['Programme'] == programme]) if not self.df_locataires.empty else 0\n", "            \n", "            # Approximation: 1 bien peut avoir 1 propriétaire et potentiellement 1 locataire\n", "            nb_biens_estimes = max(nb_proprietaires, nb_locataires) if nb_proprietaires > 0 or nb_locataires > 0 else 0\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Nb_Biens_Estimes': nb_biens_estimes,\n", "                'Nb_Proprietaires': nb_proprietaires,\n", "                'Nb_Locataires': nb_locataires\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_biens_en_location_par_proprietaire(self):\n", "        \"\"\"\n", "        Retourne le nombre de biens en location par propriétaire\n", "        \"\"\"\n", "        if self.df_proprietaires.empty or self.df_locataires.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Proprietaire', 'Nb_Biens_Loues'])\n", "        \n", "        results = []\n", "        \n", "        # Grouper par programme\n", "        for programme in self.df_proprietaires['Programme'].unique():\n", "            proprietaires_prog = self.df_proprietaires[self.df_proprietaires['Programme'] == programme]\n", "            locataires_prog = self.df_locataires[self.df_locataires['Programme'] == programme]\n", "            \n", "            # Pour chaque propriétaire, estimer le nombre de biens loués\n", "            for _, prop in proprietaires_prog.iterrows():\n", "                proprietaire_nom = f\"{prop.get('name', '')} {prop.get('prenoms', '')}\".strip()\n", "                \n", "                # Estimation simple: ratio locataires/propriétaires dans le programme\n", "                nb_locataires_prog = len(locataires_prog)\n", "                nb_proprietaires_prog = len(proprietaires_prog)\n", "                \n", "                if nb_proprietaires_prog > 0:\n", "                    biens_loues_estimes = round(nb_locataires_prog / nb_proprietaires_prog, 1)\n", "                else:\n", "                    biens_loues_estimes = 0\n", "                \n", "                results.append({\n", "                    'Programme': programme,\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Loues': biens_loues_estimes\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_programme_et_periode(self):\n", "        \"\"\"\n", "        Retourne le nombre total d'événements par programme et par période (année)\n", "        \"\"\"\n", "        if self.df_evenements.empty:\n", "            return pd.DataFrame(columns=['Programme', '<PERSON>e', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        \n", "        # Extraire l'année de la date de début si disponible\n", "        df_events_copy = self.df_evenements.copy()\n", "        \n", "        # Essayer d'extraire l'année de date_debut\n", "        if 'date_debut' in df_events_copy.columns:\n", "            df_events_copy['annee'] = pd.to_datetime(df_events_copy['date_debut'], errors='coerce').dt.year\n", "        else:\n", "            df_events_copy['annee'] = 'Non définie'\n", "        \n", "        # Grouper par programme et année\n", "        grouped = df_events_copy.groupby(['Programme', 'annee']).size().reset_index(name='Nb_Evenements')\n", "        \n", "        for _, row in grouped.iterrows():\n", "            results.append({\n", "                'Programme': row['Programme'],\n", "                'Annee': row['annee'],\n", "                'Nb_Evenements': row['Nb_Evenements']\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_organisateur(self):\n", "        \"\"\"\n", "        Retourne le nombre d'événements organisés par les propriétaires et locataires\n", "        \"\"\"\n", "        if self.df_evenements.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Type_Organisateur', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        \n", "        for programme in self.df_evenements['Programme'].unique():\n", "            events_prog = self.df_evenements[self.df_evenements['Programme'] == programme]\n", "            \n", "            # Ré<PERSON><PERSON>rer les noms des propriétaires et locataires pour ce programme\n", "            proprietaires_prog = self.df_proprietaires[self.df_proprietaires['Programme'] == programme] if not self.df_proprietaires.empty else pd.DataFrame()\n", "            locataires_prog = self.df_locataires[self.df_locataires['Programme'] == programme] if not self.df_locataires.empty else pd.DataFrame()\n", "            \n", "            # C<PERSON>er des listes de noms complets\n", "            noms_proprietaires = []\n", "            if not proprietaires_prog.empty:\n", "                noms_proprietaires = [f\"{row.get('name', '')} {row.get('prenoms', '')}\".strip().upper() \n", "                                    for _, row in proprietaires_prog.iterrows()]\n", "            \n", "            noms_locataires = []\n", "            if not locataires_prog.empty:\n", "                noms_locataires = [f\"{row.get('name', '')} {row.get('prenoms', '')}\".strip().upper() \n", "                                 for _, row in locataires_prog.iterrows()]\n", "            \n", "            # Compter les événements par type d'organisateur\n", "            nb_events_proprietaires = 0\n", "            nb_events_locataires = 0\n", "            nb_events_autres = 0\n", "            \n", "            for _, event in events_prog.iterrows():\n", "                auteur = str(event.get('auteur', '')).strip().upper()\n", "                \n", "                if any(nom in auteur for nom in noms_proprietaires if nom):\n", "                    nb_events_proprietaires += 1\n", "                elif any(nom in auteur for nom in noms_locataires if nom):\n", "                    nb_events_locataires += 1\n", "                else:\n", "                    nb_events_autres += 1\n", "            \n", "            # Ajouter les résultats\n", "            results.extend([\n", "                {'Programme': programme, 'Type_Organisateur': '<PERSON><PERSON><PERSON><PERSON>taire<PERSON>', 'Nb_Evenements': nb_events_proprietaires},\n", "                {'Programme': programme, 'Type_Organisateur': 'Locataires', 'Nb_Evenements': nb_events_locataires},\n", "                {'Programme': programme, 'Type_Organisateur': 'Autres', 'Nb_Evenements': nb_events_autres}\n", "            ])\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_incidents_par_programme(self):\n", "        \"\"\"\n", "        Retourne le nombre d'incidents par programme\n", "        \"\"\"\n", "        if self.df_incidents.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Nb_Incidents', 'Incidents_Resolus', 'Incidents_En_Cours'])\n", "        \n", "        results = []\n", "        \n", "        # Grouper par programme\n", "        for programme in self.df_incidents['Programme'].unique():\n", "            incidents_prog = self.df_incidents[self.df_incidents['Programme'] == programme]\n", "            \n", "            nb_total = len(incidents_prog)\n", "            \n", "            # Compter par statut si la colonne existe\n", "            if 'status' in incidents_prog.columns:\n", "                nb_resolus = len(incidents_prog[incidents_prog['status'].str.contains('résolu|fermé|terminé', case=False, na=False)])\n", "                nb_en_cours = nb_total - nb_resolus\n", "            else:\n", "                nb_resolus = 0\n", "                nb_en_cours = nb_total\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Nb_Incidents': nb_total,\n", "                'Incidents_Resolus': nb_resolus,\n", "                'Incidents_En_Cours': nb_en_cours\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def generer_rapport_complet(self):\n", "        \"\"\"\n", "        Génère un rapport complet avec toutes les métriques\n", "        \"\"\"\n", "        print(\"RAPPORT COMPLET E-SYNDIC\")\n", "        print(\"=\"*50)\n", "        \n", "        # 1. Propriétaires et locataires par programme\n", "        print(\"\\n1. PROPRIÉTAIRES ET LOCATAIRES PAR PROGRAMME\")\n", "        df_prop_loc = self.get_total_proprietaires_locataires_par_programme()\n", "        display(df_prop_loc)\n", "        \n", "        # 2. Nombre de biens par programme\n", "        print(\"\\n2. NOMBRE DE BIENS PAR PROGRAMME\")\n", "        df_biens = self.get_nombre_biens_par_programme()\n", "        display(df_biens)\n", "        \n", "        # 3. Biens en location par propriétaire\n", "        print(\"\\n3. BIENS EN LOCATION PAR PROPRIÉTAIRE\")\n", "        df_location = self.get_biens_en_location_par_proprietaire()\n", "        if not df_location.empty:\n", "            display(df_location)\n", "        else:\n", "            # Afficher un DataFrame vide avec les colonnes\n", "            df_vide = pd.DataFrame(columns=['Programme', 'Proprietaire', 'Nb_Biens_Loues'])\n", "            display(df_vide)\n", "        \n", "        # 4. Événements par programme et période\n", "        print(\"\\n4. ÉVÉNEMENTS PAR PROGRAMME ET PÉRIODE\")\n", "        df_events_periode = self.get_evenements_par_programme_et_periode()\n", "        if not df_events_periode.empty:\n", "            display(df_events_periode)\n", "        else:\n", "            # Afficher un DataFrame vide avec les colonnes\n", "            df_vide = pd.DataFrame(columns=['Programme', 'Annee', 'Nb_Evenements'])\n", "            display(df_vide)\n", "        \n", "        # 5. Événements par organisateur\n", "        print(\"\\n5. ÉVÉNEMENTS PAR TYPE D'ORGANISATEUR\")\n", "        df_events_org = self.get_evenements_par_organisateur()\n", "        if not df_events_org.empty:\n", "            display(df_events_org)\n", "        else:\n", "            # Afficher un DataFrame vide avec les colonnes\n", "            df_vide = pd.DataFrame(columns=['Programme', 'Type_Organisateur', 'Nb_Evenements'])\n", "            display(df_vide)\n", "        \n", "        # 6. Incidents par programme\n", "        print(\"\\n6. INCIDENTS PAR PROGRAMME\")\n", "        df_incidents = self.get_incidents_par_programme()\n", "        if not df_incidents.empty:\n", "            display(df_incidents)\n", "        else:\n", "            # Afficher un DataFrame vide avec les colonnes\n", "            df_vide = pd.DataFrame(columns=['Programme', 'Nb_Incidents', 'Incidents_Resolus', 'Incidents_En_Cours'])\n", "            display(df_vide)\n", "        \n", "        return {\n", "            'proprietaires_locataires': df_prop_loc,\n", "            'biens': df_biens,\n", "            'locations': df_location,\n", "            'evenements_periode': df_events_periode,\n", "            'evenements_organisateur': df_events_org,\n", "            'incidents': df_incidents\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Proprietaires"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO ETOILE\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [name, prenoms, email, contact]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO BNETD PHASE 1\n", " Nombre de propriétaires: 74\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BLEOUE</td>\n", "      <td>EHUI ROGER</td>\n", "      <td>rog<PERSON><PERSON><PERSON><PERSON>@hotmail.com</td>\n", "      <td>0707037922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KONAN</td>\n", "      <td>DOMINIQUE KOUADIO</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 06 82 85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ASSOUMOU</td>\n", "      <td>KOKO</td>\n", "      <td>il<PERSON><EMAIL></td>\n", "      <td>07 57 97 49 23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>YAO</td>\n", "      <td>GAISIE DORGELES</td>\n", "      <td>dorge<PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>0757320032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KOUAKOU</td>\n", "      <td>KOUAME BENOIT</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 58 76 75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>KOUAME</td>\n", "      <td>GHISLAINE ARMANDE</td>\n", "      <td>g<PERSON><PERSON><EMAIL></td>\n", "      <td>01 41 97 22 08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>DIOMANDE</td>\n", "      <td>MAH</td>\n", "      <td>madio<PERSON><EMAIL></td>\n", "      <td>07 07 22 13 96</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>SYLLA</td>\n", "      <td>MARIAME</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 87 14 41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>TRAORE</td>\n", "      <td>HADJA AMINATA BINTA</td>\n", "      <td><EMAIL></td>\n", "      <td>0707119080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>YEO</td>\n", "      <td>ISSA</td>\n", "      <td><EMAIL></td>\n", "      <td>07 09 35 35 90</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>74 rows × 4 columns</p>\n", "</div>"], "text/plain": ["        name              prenoms                    email         contact\n", "0     BLEOUE           EHUI ROGER  roger<PERSON><PERSON><EMAIL>      0707037922\n", "1      KONAN    DOMINIQUE KOUADIO     <EMAIL>  07 07 06 82 85\n", "2   ASSOUMOU                 KOKO       <EMAIL>  07 57 97 49 23\n", "3        YAO      GAISIE DORGELES    <EMAIL>      0757320032\n", "4    KOUAKOU        KOUAME BENOIT    <EMAIL>  07 07 58 76 75\n", "..       ...                  ...                      ...             ...\n", "69    KOUAME    GHISLAINE ARMANDE      <EMAIL>  01 41 97 22 08\n", "70  DIOMANDE                  MAH      <EMAIL>  07 07 22 13 96\n", "71     SYLLA              MARIAM<PERSON>           <EMAIL>  07 07 87 14 41\n", "72    TRAORE  HADJA AMINATA BINTA      <EMAIL>      0707119080\n", "73       YEO                 ISSA            <EMAIL>  07 09 35 35 90\n", "\n", "[74 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: SYMPHONIA\n", " Nombre de propriétaires: 90\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AKOU</td>\n", "      <td>YAPOIDOU AUGUSTIN</td>\n", "      <td><EMAIL></td>\n", "      <td>0708580000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>COULIBALY</td>\n", "      <td>KAFANA DANIEL</td>\n", "      <td>kafan<PERSON>@yahoo.fr</td>\n", "      <td>0707539988</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FOFANA</td>\n", "      <td>MOULOUKOU SOULEYMANE</td>\n", "      <td><EMAIL></td>\n", "      <td>0707683251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AFFRO</td>\n", "      <td>STEPHANE</td>\n", "      <td><EMAIL></td>\n", "      <td>0778722973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BABA</td>\n", "      <td>MARIAM</td>\n", "      <td><EMAIL></td>\n", "      <td>0707372865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>BAMBA</td>\n", "      <td>MASSIAMI</td>\n", "      <td><EMAIL></td>\n", "      <td>0757441908</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>SCI</td>\n", "      <td>MA-NELLY</td>\n", "      <td><EMAIL></td>\n", "      <td>0709645849</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>SCI</td>\n", "      <td>L'HARMATTAN</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>0505100495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>LOGON</td>\n", "      <td>DIDIER</td>\n", "      <td><EMAIL></td>\n", "      <td>0707681607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>KOUAKOU</td>\n", "      <td>ATCHELAUD LINA MORELLE DESIREE</td>\n", "      <td><EMAIL></td>\n", "      <td>0707701035</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 4 columns</p>\n", "</div>"], "text/plain": ["         name                         prenoms  \\\n", "0        AKOU               YAPOIDOU AUGUSTIN   \n", "1   COULIBALY                   KAFANA DANIEL   \n", "2      FOFANA            MOULOUKOU SOULEYMANE   \n", "3       AFFRO                        STEPHANE   \n", "4        BABA                          MARIAM   \n", "..        ...                             ...   \n", "85      BAMBA                        MASSIAMI   \n", "86        SCI                        MA-NELLY   \n", "87        SCI                     L'HARMATTAN   \n", "88      LOGON                          DIDIER   \n", "89    KOUAKOU  ATCHELAUD LINA MORELLE DESIREE   \n", "\n", "                                  email     contact  \n", "0               <EMAIL>  0708580000  \n", "1                      <EMAIL>  0707539988  \n", "2                     <EMAIL>  0707683251  \n", "3   <EMAIL>  0778722973  \n", "4              <EMAIL>  0707372865  \n", "..                                  ...         ...  \n", "85                   <EMAIL>  0757441908  \n", "86                 <EMAIL>  0709645849  \n", "87               stephane<PERSON>f<PERSON>@yahoo.fr  0505100495  \n", "88       <EMAIL>  0707681607  \n", "89          <EMAIL>  0707701035  \n", "\n", "[90 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, programme_row in df_clean.iterrows():\n", "    programme_nom = programme_row['Programme']\n", "    analyser_proprietaires(programme_nom, df_proprietaires)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AFFICHAGE DES LOCATAIRES"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO ETOILE\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [name, prenoms, email, contact]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO BNETD PHASE 1\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [name, prenoms, email, contact]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: SYMPHONIA\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>prenoms</th>\n", "      <th>email</th>\n", "      <th>contact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [name, prenoms, email, contact]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, programme_row in df_clean.iterrows():\n", "    programme_nom = programme_row['Programme']\n", "    analyser_locataires(programme_nom, df_locataires)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## ANALYSE DES CHARGES"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO ETOILE\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type de charge</th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Charges régulières</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Charges payées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Charges impayées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Charges exceptionnelles</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Exceptionnelles payées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Exceptionnelles impayées</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Type de charge Montant (XOF)\n", "0        Charges régulières             0\n", "1            Charges payées             0\n", "2          Charges impayées             0\n", "3   Charges exceptionnelles             0\n", "4    Exceptionnelles payées             0\n", "5  Exceptionnelles impayées             0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO BNETD PHASE 1\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type de charge</th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Charges régulières</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Charges payées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Charges impayées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Charges exceptionnelles</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Exceptionnelles payées</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Exceptionnelles impayées</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Type de charge Montant (XOF)\n", "0        Charges régulières             0\n", "1            Charges payées             0\n", "2          Charges impayées             0\n", "3   Charges exceptionnelles             0\n", "4    Exceptionnelles payées             0\n", "5  Exceptionnelles impayées             0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: SYMPHONIA\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type de charge</th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Charges régulières</td>\n", "      <td>47,625,000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Charges payées</td>\n", "      <td>139,575,000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Charges impayées</td>\n", "      <td>47,625,000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Charges exceptionnelles</td>\n", "      <td>455,000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Exceptionnelles payées</td>\n", "      <td>10,840,000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Exceptionnelles impayées</td>\n", "      <td>455,000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Type de charge Montant (XOF)\n", "0        Charges régulières    47,625,000\n", "1            Charges payées   139,575,000\n", "2          Charges impayées    47,625,000\n", "3   Charges exceptionnelles       455,000\n", "4    Exceptionnelles payées    10,840,000\n", "5  Exceptionnelles impayées       455,000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, programme_row in df_clean.iterrows():\n", "    programme_nom = programme_row['Programme']\n", "    charges = programme_row.get('Charges')\n", "    analyser_charges(programme_nom, charges)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AFF<PERSON><PERSON><PERSON> DES INCIDENTS"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO ETOILE\n", " Nombre d'incidents: 0\n", " Liste des incidents:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reference</th>\n", "      <th>date</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>priorite</th>\n", "      <th>secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [reference, date, auteur, status, priorite, secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO BNETD PHASE 1\n", " Nombre d'incidents: 0\n", " Liste des incidents:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reference</th>\n", "      <th>date</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>priorite</th>\n", "      <th>secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [reference, date, auteur, status, priorite, secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: SYMPHONIA\n", " Nombre d'incidents: 61\n", " Liste des incidents:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reference</th>\n", "      <th>date</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>priorite</th>\n", "      <th>secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>250422091253</td>\n", "      <td>25-04-2022 09:12</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>240422022613</td>\n", "      <td>24-04-2022 14:26</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>160422092638</td>\n", "      <td>16-04-2022 09:26</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>Electricité</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>270222092002</td>\n", "      <td>27-02-2022 09:20</td>\n", "      <td>MOUSOUNDA ANDREA VIRGINIE</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>Non défini</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>270222075142</td>\n", "      <td>27-02-2022 07:51</td>\n", "      <td>DIBI SERGE PACOME</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>BTP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>151120053559</td>\n", "      <td>15-11-2020 17:35</td>\n", "      <td>Inconnu</td>\n", "      <td>Traité</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>BTP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>151120052649</td>\n", "      <td>15-11-2020 17:26</td>\n", "      <td>Inconnu</td>\n", "      <td>Traité</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>BTP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>151120052433</td>\n", "      <td>15-11-2020 17:24</td>\n", "      <td>Inconnu</td>\n", "      <td>Traité</td>\n", "      <td>Haute</td>\n", "      <td>BTP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>151120052311</td>\n", "      <td>15-11-2020 17:23</td>\n", "      <td>Inconnu</td>\n", "      <td>Traité</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>BTP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>151120051745</td>\n", "      <td>15-11-2020 17:17</td>\n", "      <td>Inconnu</td>\n", "      <td>Traité</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>BTP</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>61 rows × 6 columns</p>\n", "</div>"], "text/plain": ["       reference              date                     auteur  status  \\\n", "0   250422091253  25-04-2022 09:12            INOOVIM INOOVIM  Traité   \n", "1   240422022613  24-04-2022 14:26            INOOVIM INOOVIM  Traité   \n", "2   160422092638  16-04-2022 09:26            INOOVIM INOOVIM  Traité   \n", "3   270222092002  27-02-2022 09:20  MOUSOUNDA ANDREA VIRGINIE  Traité   \n", "4   270222075142  27-02-2022 07:51          DIBI SERGE PACOME  Traité   \n", "..           ...               ...                        ...     ...   \n", "56  151120053559  15-11-2020 17:35                    <PERSON><PERSON><PERSON>   \n", "57  151120052649  15-11-2020 17:26                    <PERSON><PERSON><PERSON>   \n", "58  151120052433  15-11-2020 17:24                    <PERSON><PERSON><PERSON>   \n", "59  151120052311  15-11-2020 17:23                    <PERSON><PERSON><PERSON>   \n", "60  151120051745  15-11-2020 17:17                    <PERSON><PERSON><PERSON>   \n", "\n", "   priorite      secteur  \n", "0     Haute  Electricité  \n", "1     Haute  Electricité  \n", "2     Haute  Electricité  \n", "3     Haute   Non défini  \n", "4     Haute          BTP  \n", "..      ...          ...  \n", "56  Moyenne          BTP  \n", "57  Moyenne          BTP  \n", "58    Haute          BTP  \n", "59    Basse          BTP  \n", "60    Basse          BTP  \n", "\n", "[61 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, programme_row in df_clean.iterrows():\n", "    programme_nom = programme_row['Programme']\n", "    analyser_incidents(programme_nom, df_incidents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AFFICHAGE DES ÉVÉNEMENTS"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO ETOILE\n", " Nombre d'événements: 0\n", " Liste des événements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>titre</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>date_debut</th>\n", "      <th>heure_debut</th>\n", "      <th>bruyant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [titre, auteur, status, date_debut, heure_debut, bruyant]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: CALLISTO BNETD PHASE 1\n", " Nombre d'événements: 0\n", " Liste des événements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>titre</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>date_debut</th>\n", "      <th>heure_debut</th>\n", "      <th>bruyant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [titre, auteur, status, date_debut, heure_debut, bruyant]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " PROGRAMME: SYMPHONIA\n", " Nombre d'événements: 7\n", " Liste des événements:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>titre</th>\n", "      <th>auteur</th>\n", "      <th>status</th>\n", "      <th>date_debut</th>\n", "      <th>heure_debut</th>\n", "      <th>bruyant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Hello world</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>10/03/2022</td>\n", "      <td>11:24</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Anniversaire de ma fille</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>14/03/2022</td>\n", "      <td>14:34</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Test</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En attente</td>\n", "      <td>07/03/2022</td>\n", "      <td>14:27</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ANNIVERSAIRE</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>En cours</td>\n", "      <td>03/12/2022</td>\n", "      <td>17:00</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Anniversaire</td>\n", "      <td>KAKPO EDOU ZACHARIE</td>\n", "      <td>En cours</td>\n", "      <td>08/14/2021</td>\n", "      <td>12:00</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>ANNIVERSAIRE VILLA 39</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>En cours</td>\n", "      <td>11/07/2021</td>\n", "      <td>12:00</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Anniversaire enfant malan villa 12</td>\n", "      <td>INOOVIM INOOVIM</td>\n", "      <td>En cours</td>\n", "      <td>06/12/2021</td>\n", "      <td>14:00</td>\n", "      <td><PERSON>ui</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                titre               auteur      status  \\\n", "0                         Hello world  KAKPO EDOU ZACHARIE  En attente   \n", "1            Anniversaire de ma fille  KAKPO EDOU ZACHARIE  En attente   \n", "2                                Test  KAKPO EDOU ZACHARIE  En attente   \n", "3                        ANNIVERSAIRE      INOOVIM INOOVIM    En cours   \n", "4                        Anniversaire  KAKPO EDOU ZACHARIE    En cours   \n", "5               ANNIVERSAIRE VILLA 39      INOOVIM INOOVIM    En cours   \n", "6  Anniversaire enfant malan villa 12      INOOVIM INOOVIM    En cours   \n", "\n", "   date_debut heure_debut bruyant  \n", "0  10/03/2022       11:24     <PERSON><PERSON>  \n", "1  14/03/2022       14:34     <PERSON><PERSON>  \n", "2  07/03/2022       14:27     <PERSON><PERSON>  \n", "3  03/12/2022       17:00     Oui  \n", "4  08/14/2021       12:00     Oui  \n", "5  11/07/2021       12:00     Oui  \n", "6  06/12/2021       14:00     O<PERSON>  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, programme_row in df_clean.iterrows():\n", "    programme_nom = programme_row['Programme']\n", "    analyser_evenements(programme_nom, df_evenements)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MÉTRIQUES AVANCÉES E-SYNDIC"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAPPORT COMPLET E-SYNDIC\n", "==================================================\n", "\n", "1. PROPRIÉTAIRES ET LOCATAIRES PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "      <th>Total_Personnes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>90</td>\n", "      <td>0</td>\n", "      <td>90</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme  Nb_Proprietaires  Nb_Locataires  Total_Personnes\n", "0         CALLISTO ETOILE                 0              0                0\n", "1  CALLISTO BNETD PHASE 1                74              0               74\n", "2               SYMPHONIA                90              0               90"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "2. NOMBRE DE BIENS PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Nb_Biens_Estimes</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme  Nb_Biens_Estimes  Nb_Proprietaires  Nb_Locataires\n", "0         CALLISTO ETOILE                 0                 0              0\n", "1  CALLISTO BNETD PHASE 1                74                74              0\n", "2               SYMPHONIA                90                90              0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "3. BIENS EN LOCATION PAR PROPRIÉTAIRE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON>_<PERSON><PERSON>_<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Nb_Biens_Loues]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "4. ÉVÉNEMENTS PAR PROGRAMME ET PÉRIODE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Nb_Evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>2021.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>2022.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Programme   <PERSON><PERSON>b_Evenements\n", "0  SYMPHONIA  2021.0              3\n", "1  SYMPHONIA  2022.0              3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "5. ÉVÉNEMENTS PAR TYPE D'ORGANISATEUR\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Type_Organisateur</th>\n", "      <th>Nb_Evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SYMPHONIA</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Locataires</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Autres</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Programme Type_Organisateur  Nb_Evenements\n", "0  SYMPHONIA     Propriétaires              4\n", "1  SYMPHONIA        Locataires              0\n", "2  SYMPHONIA            Autres              3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "6. INCIDENTS PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Nb_Incidents</th>\n", "      <th>Incidents_Resolus</th>\n", "      <th>Incidents_En_Cours</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>61</td>\n", "      <td>0</td>\n", "      <td>61</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Programme  Nb_Incidents  Incidents_Resolus  Incidents_En_Cours\n", "0  SYMPHONIA            61                  0                  61"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création de l'instance de la classe de métriques\n", "metrics = ESyndicMetrics(\n", "    df_clean=df_clean,\n", "    df_proprietaires=df_proprietaires,\n", "    df_locataires=df_locataires,\n", "    df_incidents=df_incidents,\n", "    df_evenements=df_evenements\n", ")\n", "\n", "# Génération du rapport complet\n", "rapport_complet = metrics.generer_rapport_complet()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MÉTRIQUES INDIVIDUELLES"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXEMPLES D'UTILISATION DES MÉTRIQUES INDIVIDUELLES\n", "============================================================\n", "\n", "1. Propriétaires et locataires par programme:\n", "Total programmes analysés: 3\n", "Programme avec le plus de personnes: SYMPHONIA\n", "\n", "2. Événements par période:\n", "Total événements: 6\n", "Répartition par année:\n", "  - 2021.0: 3 événements\n", "  - 2022.0: 3 événements\n", "\n", "3. Incidents par programme:\n", "Total incidents: 61\n", "Incidents résolus: 0\n", "Taux de résolution: 0.0%\n"]}], "source": ["# Exemple d'utilisation des métriques individuelles\n", "print(\"\\nEXEMPLES D'UTILISATION DES MÉTRIQUES INDIVIDUELLES\")\n", "print(\"=\"*60)\n", "\n", "# 1. Propriétaires et locataires par programme\n", "print(\"\\n1. Propriétaires et locataires par programme:\")\n", "df_prop_loc = metrics.get_total_proprietaires_locataires_par_programme()\n", "print(f\"Total programmes analysés: {len(df_prop_loc)}\")\n", "if not df_prop_loc.empty:\n", "    print(f\"Programme avec le plus de personnes: {df_prop_loc.loc[df_prop_loc['Total_Personnes'].idxmax(), 'Programme']}\")\n", "\n", "# 2. Événements par période\n", "print(\"\\n2. Événements par période:\")\n", "df_events = metrics.get_evenements_par_programme_et_periode()\n", "if not df_events.empty:\n", "    total_events = df_events['Nb_Evenements'].sum()\n", "    print(f\"Total événements: {total_events}\")\n", "    if 'Annee' in df_events.columns:\n", "        events_par_annee = df_events.groupby('Annee')['Nb_Evenements'].sum()\n", "        print(\"Répartition par année:\")\n", "        for annee, nb in events_par_annee.items():\n", "            print(f\"  - {annee}: {nb} événements\")\n", "else:\n", "    print(\"Aucun événement trouvé\")\n", "\n", "# 3. Incidents par programme\n", "print(\"\\n3. Incidents par programme:\")\n", "df_incidents_metrics = metrics.get_incidents_par_programme()\n", "if not df_incidents_metrics.empty:\n", "    total_incidents = df_incidents_metrics['Nb_Incidents'].sum()\n", "    total_resolus = df_incidents_metrics['Incidents_Resolus'].sum()\n", "    print(f\"Total incidents: {total_incidents}\")\n", "    print(f\"Incidents résolus: {total_resolus}\")\n", "    if total_incidents > 0:\n", "        taux_resolution = (total_resolus / total_incidents) * 100\n", "        print(f\"Taux de résolution: {taux_resolution:.1f}%\")\n", "else:\n", "    print(\"Aucun incident trouvé\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## SAUVEGARD<PERSON> DES MÉTRIQUES"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Section de sauvegarde mise en commentaire - Toutes les métriques sont affichées sous forme de DataFrames\n"]}], "source": ["# # Sauvegarde des métriques en CSV pour réutilisation\n", "# print(\"\\nSAUVEGARDE DES MÉTRIQUES\")\n", "# print(\"=\"*40)\n", "# \n", "# try:\n", "#     # <PERSON><PERSON><PERSON><PERSON> chaque métrique\n", "#     rapport_complet['proprietaires_locataires'].to_csv('esyndic_proprietaires_locataires.csv', index=False)\n", "#     print(\"esyndic_proprietaires_locataires.csv sauvegardé\")\n", "#     \n", "#     rapport_complet['biens'].to_csv('esyndic_biens.csv', index=False)\n", "#     print(\"esyndic_biens.csv sauvegardé\")\n", "#     \n", "#     if not rapport_complet['locations'].empty:\n", "#         rapport_complet['locations'].to_csv('esyndic_locations.csv', index=False)\n", "#         print(\"esyndic_locations.csv sauvegardé\")\n", "#     \n", "#     if not rapport_complet['evenements_periode'].empty:\n", "#         rapport_complet['evenements_periode'].to_csv('esyndic_evenements_periode.csv', index=False)\n", "#         print(\"esyndic_evenements_periode.csv sauvegardé\")\n", "#     \n", "#     if not rapport_complet['evenements_organisateur'].empty:\n", "#         rapport_complet['evenements_organisateur'].to_csv('esyndic_evenements_organisateur.csv', index=False)\n", "#         print(\"esyndic_evenements_organisateur.csv sauvegardé\")\n", "#     \n", "#     if not rapport_complet['incidents'].empty:\n", "#         rapport_complet['incidents'].to_csv('esyndic_incidents.csv', index=False)\n", "#         print(\"esyndic_incidents.csv sauvegardé\")\n", "#     \n", "#     print(\"\\nToutes les métriques ont été sauvegardées avec succès !\")\n", "#     \n", "# except Exception as e:\n", "#     print(f\"Erreur lors de la sauvegarde: {str(e)}\")\n", "\n", "print(\"Section de sauvegarde mise en commentaire - Toutes les métriques sont affichées sous forme de DataFrames\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}