import pandas as pd
import json
import ast

csv_file = 'G_locative.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

# Création du DataFrame principal directement depuis le CSV
df_biens = df_raw.copy()
print(f"DataFrame créé avec {len(df_biens)} biens")

# Affichage des colonnes disponibles
print("\nColonnes disponibles:")
print(df_biens.columns.tolist())

# Affichage des données de base
print("\n Aperçu des données:")
display(df_biens.head())

class Cleaning:
    def __init__(self, df):
        self.df = df.copy()

    def clean(self):
        # Supprimer colonne id
        self.df.drop(columns=['id'], inplace=True)
        # Renommer colonnes
        self.df.rename(columns={
            'libelle': 'Bien',
            'adresse': 'Adresse',
            'type_id': 'Type_id',
            'created_at': 'Created_at',
            'proprietaires': 'Proprietaires',
            'locataires': 'Locataires',
            'charges': 'Charges',
            'totalCharges': 'TotalCharges',
            'totaLoyer': 'TotalLoyer',
            'totalImpayer': 'TotalImpayer',
            'type': 'Type',
            'actifs': 'Actifs'
        }, inplace=True)
        return self.df

cleaner = Cleaning(df_biens)
df_clean = cleaner.clean()
df_clean.head()

def explode_column(df, col_name, parent_col='Bien'):
    """Éclate une colonne contenant des listes d'objets"""
    rows = []
    
    for _, row in df.iterrows():
        data_list = row.get(col_name)
        
        # Vérifier que c'est une liste non vide
        if isinstance(data_list, str) and data_list.strip() and data_list != '[]':
            try:
                # Parser la chaîne JSON
                data_list = ast.literal_eval(data_list)
            except:
                continue
        
        if isinstance(data_list, list) and len(data_list) > 0:
            for item in data_list:
                if isinstance(item, dict):
                    # Ajouter le nom du bien parent
                    item_copy = item.copy()
                    item_copy[parent_col] = row[parent_col]
                    rows.append(item_copy)
    
    return pd.DataFrame(rows)

# Éclatement des propriétaires
df_proprietaires = explode_column(df_clean, 'Proprietaires')
print(f"Propriétaires extraits: {len(df_proprietaires)}")

# Éclatement des locataires
df_locataires = explode_column(df_clean, 'Locataires')
print(f"Locataires extraits: {len(df_locataires)}")

# Éclatement des charges
df_charges = explode_column(df_clean, 'Charges')
print(f"Charges extraites: {len(df_charges)}")

# Éclatement des actifs
df_actifs = explode_column(df_clean, 'Actifs')
print(f"Actifs extraits: {len(df_actifs)}")

def analyser_proprietaires(bien_nom, df_proprietaires):
    """Analyse et affiche les propriétaires d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les propriétaires pour ce bien
    bien_proprietaires = df_proprietaires[df_proprietaires['Bien'] == bien_nom] if not df_proprietaires.empty else pd.DataFrame()
    
    print(f" Nombre de propriétaires: {len(bien_proprietaires)}")
    print(" Liste des propriétaires:")
    
    # Créer un DataFrame pour l'affichage
    if not bien_proprietaires.empty and 'user' in bien_proprietaires.columns:
        proprietaires_data = []
        for _, prop in bien_proprietaires.iterrows():
            user_info = prop.get('user', {})
            if isinstance(user_info, dict):
                proprietaires_data.append({
                    'Nom': user_info.get('name', 'N/A'),
                    'Email': user_info.get('email', 'N/A'),
                    'Role': user_info.get('role_id', 'N/A'),
                    'Tantieme': prop.get('tantieme', 'N/A'),
                    'Type': prop.get('type_user', 'N/A')
                })
        
        if proprietaires_data:
            df_display = pd.DataFrame(proprietaires_data)
            display(df_display.reset_index(drop=True))
        else:
            df_empty = pd.DataFrame(columns=['Nom', 'Email', 'Role', 'Tantieme', 'Type'])
            display(df_empty)
    else:
        df_empty = pd.DataFrame(columns=['Nom', 'Email', 'Role', 'Tantieme', 'Type'])
        display(df_empty)

def analyser_locataires(bien_nom, df_locataires):
    """Analyse et affiche les locataires d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les locataires pour ce bien
    bien_locataires = df_locataires[df_locataires['Bien'] == bien_nom] if not df_locataires.empty else pd.DataFrame()
    
    print(f" Nombre de locataires: {len(bien_locataires)}")
    print(" Liste des locataires:")
    
    # Créer un DataFrame pour l'affichage
    if not bien_locataires.empty:
        locataires_data = []
        for _, loc in bien_locataires.iterrows():
            locataires_data.append({
                'Nom': loc.get('nom', 'N/A'),
                'Prenom': loc.get('prenom', 'N/A'),
                'Email': loc.get('email', 'N/A'),
                'Contact': loc.get('contact', 'N/A'),
                'Indicatif': loc.get('indicatif', 'N/A'),
                'NCC': loc.get('ncc', 'N/A'),
                'Secteur': loc.get('secteur', 'N/A')
            })
        
        df_display = pd.DataFrame(locataires_data)
        display(df_display.reset_index(drop=True))
    else:
        df_empty = pd.DataFrame(columns=['Nom', 'Prenom', 'Email', 'Contact', 'Indicatif', 'NCC', 'Secteur'])
        display(df_empty)

def analyser_charges(bien_nom, charges_data):
    """Analyse et affiche les charges d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    print(" Détail des charges:")
    
    # Créer un DataFrame pour l'affichage
    if isinstance(charges_data, str) and charges_data.strip() and charges_data != '[]':
        try:
            charges_list = ast.literal_eval(charges_data)
            if isinstance(charges_list, list) and len(charges_list) > 0:
                charges_data_list = []
                for charge in charges_list:
                    if isinstance(charge, dict):
                        charges_data_list.append({
                            'ID': charge.get('id', 'N/A'),
                            'Libelle': charge.get('libelle', 'N/A'),
                            'Montant (XOF)': f"{charge.get('montant', 0):,.0f}"
                        })
                
                if charges_data_list:
                    df_display = pd.DataFrame(charges_data_list)
                    display(df_display.reset_index(drop=True))
                else:
                    df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Montant (XOF)'])
                    display(df_empty)
            else:
                df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Montant (XOF)'])
                display(df_empty)
        except:
            print(" Erreur lors du parsing des charges")
            df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Montant (XOF)'])
            display(df_empty)
    else:
        df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Montant (XOF)'])
        display(df_empty)

def analyser_actifs(bien_nom, df_actifs):
    """Analyse et affiche les actifs d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les actifs pour ce bien
    bien_actifs = df_actifs[df_actifs['Bien'] == bien_nom] if not df_actifs.empty else pd.DataFrame()
    
    print(f" Nombre d'actifs: {len(bien_actifs)}")
    print(" Liste des actifs:")
    
    # Créer un DataFrame pour l'affichage
    if not bien_actifs.empty:
        actifs_data = []
        for _, actif in bien_actifs.iterrows():
            type_info = actif.get('type', {})
            type_libelle = type_info.get('libelle', 'N/A') if isinstance(type_info, dict) else 'N/A'
            actifs_data.append({
                'ID': actif.get('id', 'N/A'),
                'Libelle': actif.get('libelle', 'N/A'),
                'Description': actif.get('description', 'N/A'),
                'Type': type_libelle,
                'Type_ID': actif.get('type_id', 'N/A')
            })
        
        df_display = pd.DataFrame(actifs_data)
        display(df_display.reset_index(drop=True))
    else:
        df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Description', 'Type', 'Type_ID'])
        display(df_empty)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_proprietaires(bien_nom, df_proprietaires)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_locataires(bien_nom, df_locataires)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    charges_data = bien_row['Charges']
    analyser_charges(bien_nom, charges_data)

# Créer un DataFrame avec tous les types de biens
types_data = []

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    type_data = bien_row['Type']
    
    if isinstance(type_data, str) and type_data.strip() and type_data != '{}':
        try:
            type_info = ast.literal_eval(type_data)
            if isinstance(type_info, dict):
                types_data.append({
                    'Bien': bien_nom,
                    'Type_ID': type_info.get('id', 'N/A'),
                    'Type': type_info.get('libelle', 'N/A')
                })
            else:
                types_data.append({
                    'Bien': bien_nom,
                    'Type_ID': 'N/A',
                    'Type': 'N/A'
                })
        except:
            types_data.append({
                'Bien': bien_nom,
                'Type_ID': 'N/A',
                'Type': 'Erreur parsing'
            })
    else:
        types_data.append({
            'Bien': bien_nom,
            'Type_ID': 'N/A',
            'Type': 'N/A'
        })

# Afficher le DataFrame des types
if types_data:
    df_types = pd.DataFrame(types_data)
    print(f"\nRésumé des types de biens ({len(df_types)} biens):")
    display(df_types.reset_index(drop=True))
    
    # Statistiques par type
    print("\nRépartition par type:")
    type_counts = df_types['Type'].value_counts().reset_index()
    type_counts.columns = ['Type', 'Nombre de biens']
    display(type_counts)
else:
    print("Aucune donnée de type disponible")

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_actifs(bien_nom, df_actifs)

class GestionLocativeMetrics:
    """
    Classe réutilisable pour calculer toutes les métriques de Gestion Locative
    """
    
    def __init__(self, df_clean, df_proprietaires, df_locataires, df_actifs):
        self.df_clean = df_clean
        self.df_proprietaires = df_proprietaires
        self.df_locataires = df_locataires
        self.df_actifs = df_actifs
    
    def get_total_proprietaires_locataires_par_bien(self):
        """
        Retourne le nombre total de propriétaires et locataires par bien
        """
        results = []
        
        for _, row in self.df_clean.iterrows():
            bien = row['Bien']
            type_bien = row.get('Type', 'Non défini')
            adresse = row.get('Adresse', 'Non définie')
            
            # Compter propriétaires
            nb_proprietaires = len(self.df_proprietaires[self.df_proprietaires['Bien'] == bien]) if not self.df_proprietaires.empty else 0
            
            # Compter locataires
            nb_locataires = len(self.df_locataires[self.df_locataires['Bien'] == bien]) if not self.df_locataires.empty else 0
            
            results.append({
                'Bien': bien,
                'Type_Bien': type_bien,
                'Adresse': adresse,
                'Nb_Proprietaires': nb_proprietaires,
                'Nb_Locataires': nb_locataires,
                'Total_Personnes': nb_proprietaires + nb_locataires,
                'Statut_Location': 'Loué' if nb_locataires > 0 else 'Libre'
            })
        
        return pd.DataFrame(results)
    
    def get_nombre_biens_par_type(self):
        """
        Retourne le nombre de biens par type
        """
        if self.df_clean.empty:
            return pd.DataFrame(columns=['Type_Bien', 'Nb_Biens', 'Nb_Loues', 'Nb_Libres', 'Taux_Occupation'])
        
        results = []
        
        # Grouper par type de bien
        for type_bien in self.df_clean['Type'].unique():
            biens_type = self.df_clean[self.df_clean['Type'] == type_bien]
            nb_biens = len(biens_type)
            
            # Compter les biens loués
            nb_loues = 0
            for _, bien_row in biens_type.iterrows():
                bien = bien_row['Bien']
                nb_locataires = len(self.df_locataires[self.df_locataires['Bien'] == bien]) if not self.df_locataires.empty else 0
                if nb_locataires > 0:
                    nb_loues += 1
            
            nb_libres = nb_biens - nb_loues
            taux_occupation = (nb_loues / nb_biens * 100) if nb_biens > 0 else 0
            
            results.append({
                'Type_Bien': type_bien,
                'Nb_Biens': nb_biens,
                'Nb_Loues': nb_loues,
                'Nb_Libres': nb_libres,
                'Taux_Occupation': round(taux_occupation, 2)
            })
        
        return pd.DataFrame(results)
    
    def get_biens_par_proprietaire(self):
        """
        Retourne le nombre de biens par propriétaire avec leurs types
        """
        if self.df_proprietaires.empty:
            return pd.DataFrame(columns=['Proprietaire', 'Nb_Biens_Total', 'Nb_Biens_Loues', 'Types_Biens', 'Biens_Details'])
        
        results = []
        
        # Grouper par propriétaire
        proprietaires_uniques = self.df_proprietaires.groupby(['name', 'prenoms', 'email']).size().reset_index(name='count')
        
        for _, prop in proprietaires_uniques.iterrows():
            proprietaire_nom = f"{prop['name']} {prop['prenoms']}".strip()
            proprietaire_email = prop['email']
            
            # Trouver tous les biens de ce propriétaire
            biens_proprietaire = self.df_proprietaires[
                (self.df_proprietaires['name'] == prop['name']) & 
                (self.df_proprietaires['prenoms'] == prop['prenoms'])
            ]
            
            nb_biens_total = len(biens_proprietaire)
            
            # Analyser chaque bien
            biens_details = []
            types_biens = set()
            nb_biens_loues = 0
            
            for _, bien_prop in biens_proprietaire.iterrows():
                bien_nom = bien_prop['Bien']
                
                # Trouver le type du bien
                bien_info = self.df_clean[self.df_clean['Bien'] == bien_nom]
                if not bien_info.empty:
                    type_bien = bien_info.iloc[0]['Type']
                    types_biens.add(type_bien)
                    
                    # Vérifier si le bien est loué
                    nb_locataires = len(self.df_locataires[self.df_locataires['Bien'] == bien_nom]) if not self.df_locataires.empty else 0
                    statut = 'Loué' if nb_locataires > 0 else 'Libre'
                    if nb_locataires > 0:
                        nb_biens_loues += 1
                    
                    biens_details.append(f"{bien_nom} ({type_bien} - {statut})")
                else:
                    biens_details.append(f"{bien_nom} (Type inconnu)")
            
            results.append({
                'Proprietaire': proprietaire_nom,
                'Email': proprietaire_email,
                'Nb_Biens_Total': nb_biens_total,
                'Nb_Biens_Loues': nb_biens_loues,
                'Nb_Biens_Libres': nb_biens_total - nb_biens_loues,
                'Taux_Location': round((nb_biens_loues / nb_biens_total * 100), 2) if nb_biens_total > 0 else 0,
                'Types_Biens': ', '.join(sorted(types_biens)),
                'Biens_Details': ' | '.join(biens_details)
            })
        
        return pd.DataFrame(results)
    
    def get_metriques_financieres(self):
        """
        Retourne les métriques financières par bien
        """
        results = []
        
        for _, row in self.df_clean.iterrows():
            bien = row['Bien']
            type_bien = row.get('Type', 'Non défini')
            
            # Extraire les données financières
            total_charges = row.get('TotalCharges', 0)
            total_loyer = row.get('TotalLoyer', 0)
            total_impaye = row.get('TotalImpayer', 0)
            
            # Calculer les métriques
            revenus_nets = total_loyer - total_charges
            taux_impaye = (total_impaye / total_loyer * 100) if total_loyer > 0 else 0
            
            results.append({
                'Bien': bien,
                'Type_Bien': type_bien,
                'Total_Loyer': total_loyer,
                'Total_Charges': total_charges,
                'Revenus_Nets': revenus_nets,
                'Total_Impaye': total_impaye,
                'Taux_Impaye': round(taux_impaye, 2),
                'Rentabilite': 'Positive' if revenus_nets > 0 else 'Négative' if revenus_nets < 0 else 'Nulle'
            })
        
        return pd.DataFrame(results)
    
    def get_resume_global(self):
        """
        Retourne un résumé global de la gestion locative
        """
        # Métriques de base
        df_prop_loc = self.get_total_proprietaires_locataires_par_bien()
        df_types = self.get_nombre_biens_par_type()
        df_financier = self.get_metriques_financieres()
        
        # Calculs globaux
        total_biens = len(self.df_clean)
        total_proprietaires = len(self.df_proprietaires) if not self.df_proprietaires.empty else 0
        total_locataires = len(self.df_locataires) if not self.df_locataires.empty else 0
        
        biens_loues = len(df_prop_loc[df_prop_loc['Statut_Location'] == 'Loué'])
        biens_libres = total_biens - biens_loues
        taux_occupation_global = (biens_loues / total_biens * 100) if total_biens > 0 else 0
        
        # Métriques financières globales
        total_loyers = df_financier['Total_Loyer'].sum()
        total_charges = df_financier['Total_Charges'].sum()
        total_impayes = df_financier['Total_Impaye'].sum()
        revenus_nets_globaux = total_loyers - total_charges
        
        resume = {
            'Total_Biens': total_biens,
            'Total_Proprietaires': total_proprietaires,
            'Total_Locataires': total_locataires,
            'Biens_Loues': biens_loues,
            'Biens_Libres': biens_libres,
            'Taux_Occupation_Global': round(taux_occupation_global, 2),
            'Total_Loyers': total_loyers,
            'Total_Charges': total_charges,
            'Revenus_Nets_Globaux': revenus_nets_globaux,
            'Total_Impayes': total_impayes,
            'Taux_Impaye_Global': round((total_impayes / total_loyers * 100), 2) if total_loyers > 0 else 0
        }
        
        return resume
    
    def generer_rapport_complet(self):
        """
        Génère un rapport complet avec toutes les métriques
        """
        print("RAPPORT COMPLET GESTION LOCATIVE")
        print("="*50)
        
        # 1. Résumé global
        print("\n1. RÉSUMÉ GLOBAL")
        resume = self.get_resume_global()
        for key, value in resume.items():
            if isinstance(value, (int, float)):
                if 'Taux' in key or 'Global' in key:
                    print(f"  - {key.replace('_', ' ')}: {value}%")
                elif 'Total' in key or 'Revenus' in key:
                    print(f"  - {key.replace('_', ' ')}: {value:,.0f} XOF")
                else:
                    print(f"  - {key.replace('_', ' ')}: {value}")
            else:
                print(f"  - {key.replace('_', ' ')}: {value}")
        
        # 2. Propriétaires et locataires par bien
        print("\n2. PROPRIÉTAIRES ET LOCATAIRES PAR BIEN")
        df_prop_loc = self.get_total_proprietaires_locataires_par_bien()
        display(df_prop_loc)
        
        # 3. Biens par type - Affichage séparé par type
        print("\n3. RÉPARTITION DES BIENS PAR TYPE")
        df_types = self.get_nombre_biens_par_type()
        display(df_types)
        
        # Affichage détaillé par type de bien
        print("\n3.1 DÉTAIL PAR TYPE DE BIEN")
        for type_bien in self.df_clean['Type'].unique():
            print(f"\nType: {type_bien}")
            biens_type = self.df_clean[self.df_clean['Type'] == type_bien]
            df_detail_type = biens_type[['Bien', 'Adresse', 'Type']].copy()
            
            # Ajouter statut de location
            statuts = []
            for _, bien_row in biens_type.iterrows():
                bien_nom = bien_row['Bien']
                nb_locataires = len(self.df_locataires[self.df_locataires['Bien'] == bien_nom]) if not self.df_locataires.empty else 0
                statuts.append('Loué' if nb_locataires > 0 else 'Libre')
            
            df_detail_type['Statut'] = statuts
            display(df_detail_type)
        
        # 4. Biens par propriétaire
        print("\n4. BIENS PAR PROPRIÉTAIRE")
        df_proprietaires = self.get_biens_par_proprietaire()
        if not df_proprietaires.empty:
            display(df_proprietaires)
        else:
            # Afficher un DataFrame vide avec les colonnes
            df_vide = pd.DataFrame(columns=['Proprietaire', 'Email', 'Nb_Biens_Total', 'Nb_Biens_Loues', 'Nb_Biens_Libres', 'Taux_Location', 'Types_Biens', 'Biens_Details'])
            display(df_vide)
        
        # 5. Métriques financières
        print("\n5. MÉTRIQUES FINANCIÈRES PAR BIEN")
        df_financier = self.get_metriques_financieres()
        display(df_financier)
        
        return {
            'resume_global': resume,
            'proprietaires_locataires': df_prop_loc,
            'biens_par_type': df_types,
            'biens_par_proprietaire': df_proprietaires,
            'metriques_financieres': df_financier
        }

# Création de l'instance de la classe de métriques
metrics_locative = GestionLocativeMetrics(
    df_clean=df_clean,
    df_proprietaires=df_proprietaires,
    df_locataires=df_locataires,
    df_actifs=df_actifs
)

# Génération du rapport complet
rapport_locative = metrics_locative.generer_rapport_complet()

# Exemple d'utilisation des métriques individuelles
print("\nEXEMPLES D'UTILISATION DES MÉTRIQUES INDIVIDUELLES")
print("="*60)

# 1. Analyse par type de bien
print("\n1. Analyse par type de bien:")
df_types = metrics_locative.get_nombre_biens_par_type()
if not df_types.empty:
    type_plus_rentable = df_types.loc[df_types['Taux_Occupation'].idxmax()]
    print(f"Type le plus occupé: {type_plus_rentable['Type_Bien']} ({type_plus_rentable['Taux_Occupation']}%)")
    print(f"Total types de biens: {len(df_types)}")

# 2. Analyse des propriétaires
print("\n2. Analyse des propriétaires:")
df_props = metrics_locative.get_biens_par_proprietaire()
if not df_props.empty:
    total_proprietaires = len(df_props)
    proprietaire_plus_actif = df_props.loc[df_props['Nb_Biens_Total'].idxmax()]
    print(f"Total propriétaires: {total_proprietaires}")
    print(f"Propriétaire le plus actif: {proprietaire_plus_actif['Proprietaire']} ({proprietaire_plus_actif['Nb_Biens_Total']} biens)")
    
    # Statistiques de location
    taux_location_moyen = df_props['Taux_Location'].mean()
    print(f"Taux de location moyen: {taux_location_moyen:.1f}%")
else:
    print("Aucun propriétaire trouvé")

# 3. Analyse financière
print("\n3. Analyse financière:")
df_financier = metrics_locative.get_metriques_financieres()
if not df_financier.empty:
    biens_rentables = len(df_financier[df_financier['Rentabilite'] == 'Positive'])
    total_biens = len(df_financier)
    print(f"Biens rentables: {biens_rentables}/{total_biens} ({biens_rentables/total_biens*100:.1f}%)")
    
    revenus_totaux = df_financier['Revenus_Nets'].sum()
    print(f"Revenus nets totaux: {revenus_totaux:,.0f} XOF")
    
    if df_financier['Total_Impaye'].sum() > 0:
        taux_impaye_moyen = df_financier['Taux_Impaye'].mean()
        print(f"Taux d'impayé moyen: {taux_impaye_moyen:.1f}%")
else:
    print("Aucune donnée financière trouvée")

# # Sauvegarde des métriques en CSV pour réutilisation
# print("\nSAUVEGARDE DES MÉTRIQUES GESTION LOCATIVE")
# print("="*50)
# 
# try:
#     # Sauvegarder chaque métrique
#     rapport_locative['proprietaires_locataires'].to_csv('gestion_locative_proprietaires_locataires.csv', index=False)
#     print("gestion_locative_proprietaires_locataires.csv sauvegardé")
#     
#     rapport_locative['biens_par_type'].to_csv('gestion_locative_biens_par_type.csv', index=False)
#     print("gestion_locative_biens_par_type.csv sauvegardé")
#     
#     if not rapport_locative['biens_par_proprietaire'].empty:
#         rapport_locative['biens_par_proprietaire'].to_csv('gestion_locative_biens_par_proprietaire.csv', index=False)
#         print("gestion_locative_biens_par_proprietaire.csv sauvegardé")
#     
#     rapport_locative['metriques_financieres'].to_csv('gestion_locative_metriques_financieres.csv', index=False)
#     print("gestion_locative_metriques_financieres.csv sauvegardé")
#     
#     # Sauvegarder le résumé global
#     resume_df = pd.DataFrame([rapport_locative['resume_global']])
#     resume_df.to_csv('gestion_locative_resume_global.csv', index=False)
#     print("gestion_locative_resume_global.csv sauvegardé")
#     
#     print("\nToutes les métriques de gestion locative ont été sauvegardées avec succès !")
#     
# except Exception as e:
#     print(f"Erreur lors de la sauvegarde: {str(e)}")

print("Section de sauvegarde mise en commentaire - Toutes les métriques sont affichées sous forme de DataFrames")